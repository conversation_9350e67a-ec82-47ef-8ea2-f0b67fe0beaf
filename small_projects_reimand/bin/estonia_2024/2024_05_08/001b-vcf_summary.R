# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




barplot = function(variant_type, input_df){


# set max
# input_df$count[input_df$count > 100000] = 100000
input_df$patient = fct_reorder(as.factor(input_df$patient), input_df$value, .desc=TRUE)

# plot raw expression
p = ggplot(input_df, aes(x = patient, y = value)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, >=2 callers)') +

facet_grid(. ~ variant_caller) +
theme(axis.text.x = element_text(size = 3))


print(p)


# plot raw expression
p = ggplot(input_df, aes(x = patient, y = value, fill = variant_caller)) + plot_theme() +
geom_bar(stat = "identity", position = "dodge") +


ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, >=2 callers)') +
theme(axis.text.x = element_text(size = 8))


print(p)



# subset to samples of interest
sub_df = input_df[grepl('R', input_df$Recurrence_type),]

p = ggplot(sub_df, aes(x = patient, y = value, fill = Recurrence_type)) + plot_theme() +
geom_bar(stat = "identity") +


ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, >=2 callers)') +

facet_grid(. ~ variant_caller) +
theme(axis.text.x = element_text(size = 3))


print(p)


# just plot mutect2
sub_df = sub_df[sub_df$variant_caller == 'mutect2',]

# sort by value
sub_df$patient = fct_reorder(as.factor(sub_df$patient), sub_df$value, .desc=TRUE)
    
p = ggplot(sub_df, aes(x = patient, y = log10(value), fill = Recurrence_type)) + plot_theme() +
geom_bar(stat = "identity") +


ggtitle(paste(variant_type, 'Mutect2')) +
xlab("Sample") + ylab('Number of variants (filtered, >=2 callers, log10)') +

theme(axis.text.x = element_text(size = 6))


print(p)




return()
}






pdf(opt$figure_file, width = 20)

# load df
df = read.csv(opt$figure_data_file, sep='\t')



for (key in c('SNPs', 'indels')){

main_df = df[df$key == key,]

# create plot
barplot(key, main_df)

}




dev.off()


print(opt$figure_file)




