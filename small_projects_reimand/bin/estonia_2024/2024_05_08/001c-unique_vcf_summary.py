# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def summarize_variants(figure_data_file):
    # read in data
    df = pd.read_csv(figure_data_file, sep='\t')

    # summarize number of mutations in each gene
    df = df.groupby(['patient', 'variant_type']).size().reset_index(name='count')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # summarize the number of variants per patient
    res_df = summarize_variants(figure_data_file)

    # save to file
    res_df.to_csv(figure_stats_file, sep='\t', index=False)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["figure_data_file=", "r_script=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


