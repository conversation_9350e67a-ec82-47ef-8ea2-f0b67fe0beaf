# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def extract_info(info_str):
    """Helper function to extract relevant information from the INFO field."""
    info_dict = {}
    
    # vep fields
    vep_fields = ['Allele', 'Consequence', 'IMPACT', 'SYMBOL', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON', 'HGVSc', 'HGVSp', 'cDNA_position', 'CDS_position', 'Protein_position', 'Amino_acids', 'Codons', 'Existing_variation', 'DISTANCE', 'STRAND', 'FLAGS', 'VARIANT_CLASS', 'SYMBOL_SOURCE', 'HGNC_ID', 'CANONICAL', 'MANE_SELECT', 'MANE_PLUS_CLINICAL', 'TSL', 'APPRIS', 'CCDS', 'ENSP', 'SWISSPROT', 'TREMBL', 'UNIPARC', 'UNIPROT_ISOFORM', 'GENE_PHENO', 'SIFT', 'PolyPhen', 'DOMAINS', 'miRNA', 'AF', 'AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'gnomADe_AF', 'gnomADe_AFR_AF', 'gnomADe_AMR_AF', 'gnomADe_ASJ_AF', 'gnomADe_EAS_AF', 'gnomADe_FIN_AF', 'gnomADe_NFE_AF', 'gnomADe_OTH_AF', 'gnomADe_SAS_AF', 'gnomADg_AF', 'gnomADg_AFR_AF', 'gnomADg_AMI_AF', 'gnomADg_AMR_AF', 'gnomADg_ASJ_AF', 'gnomADg_EAS_AF', 'gnomADg_FIN_AF', 'gnomADg_MID_AF', 'gnomADg_NFE_AF', 'gnomADg_OTH_AF', 'gnomADg_SAS_AF', 'MAX_AF', 'MAX_AF_POPS', 'FREQS', 'CLIN_SIG', 'SOMATIC', 'PHENO', 'PUBMED', 'MOTIF_NAME', 'MOTIF_POS', 'HIGH_INF_POS', 'MOTIF_SCORE_CHANGE', 'TRANSCRIPTION_FACTORS']

    # collect the CSQ fields (VEP annotations)
    info_str = info_str.split(";")[-1].split(",")[0]

    # split the fields
    for i, item in enumerate(info_str.split('|')):
        info_dict[vep_fields[i]] = item

    return info_dict

def classify_variant(info_dict, filter_status, min_freq):
    """Classify the variant based on INFO and FILTER fields."""
    # impact of variant
    sift = info_dict.get('SIFT')
    polyphen = info_dict.get('PolyPhen')
    variant_classification = info_dict.get('Allele')[-1]

    # Population frequencies from INFO field including the 1000 Genomes Project (first 5 fields) and gnomAD (MAX_AF)
    pop_freq_keys = ['AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'MAX_AF']
    pop_freqs = [float(info_dict.get(key, 0)) if info_dict.get(key) != '' else 0 for key in pop_freq_keys]
    
    # record population and pathogenic probabilities
    population_variant = 'Somatic'
    pathogenic_variant = 'Likely benign'
    variant_type = 'SNV'

    if filter_status.lower() == 'germline':
        population_variant = 'Population'
    # Consider a variant benign if any population frequency is above a threshold (e.g., 1%)
    elif any(freq > min_freq for freq in pop_freqs):
        population_variant = 'Population'
    
    if 'deleterious' in sift.lower():
        pathogenic_variant = 'Likely damaging'
    elif 'damaging' in polyphen.lower():
        pathogenic_variant = 'Likely damaging'

    if variant_classification in ['-', '+']:
        variant_type = 'Indel'
    
    return [population_variant, pathogenic_variant, variant_type]


def classify_variants(df, cutoff):
    """
    Classify variants in a dataframe as likely benign/germline or likely damaging/somatic.
    
    Parameters:
    df (pd.DataFrame): Dataframe containing VEP annotated variants with necessary columns.
    
    Returns:
    pd.DataFrame: Dataframe with an additional column 'Classification' indicating the variant classification.
    """
    classifications = []
    for _, row in df.iterrows():
        info_dict = extract_info(row['INFO'])
        classification = classify_variant(info_dict, row['FILTER'], cutoff)
        classifications.append(classification)

    df.loc[:,['population_classification', 'pathogenic_classification', 'variant_type']] = classifications
    
    return df


def load_df(args):
    file, cutoff = args
    
    # read the vcf file
    df = pd.read_csv(file, sep='\t', comment='#', compression='gzip', header=None)
    df.columns = ['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'SAMPLE']
    
    # extract the patient id
    df['patient'] = file.split("/")[-1].split("-")[0]

    # subset to unique variants
    variant_ids = df['CHROM'].astype(str) + '_' + df['POS'].astype(str) + '_' + df['REF'] + '_' + df['ALT']
    df['variant_id'] = variant_ids
    df = df.drop_duplicates(subset='variant_id')
    
    # classify the variants
    df = classify_variants(df, cutoff)

    # add patient id
    df['patient'] = file.split("/")[-2].split("-")[0]
    
    return df


def load_and_process_dfs(file_list, annotated_variants_file, samples_description_file, sample_recurrence_file, cutoff, threads):
    params_list = [(file, cutoff) for file in file_list]
    
    # multi-thread calculate p-values and fc
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(load_df, params_list))

    # collect converted dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    # save vcf to file
    res_df.to_csv(annotated_variants_file, sep='\t', index=False)

    print(res_df.head())
    print(res_df.columns)

    # summarize number of SNVs and Indels, keep the Classification
    res_df = res_df.groupby(['patient', 'variant_type', 'population_classification', 'pathogenic_classification']).size().reset_index(name='counts')

    # add the source of the sample
    samples_df = pd.read_csv(samples_description_file, sep='\t')
    
    # subset to common samples and merge
    res_df['patient'] = res_df['patient'].astype("<U64")
    samples_df['patient'] = samples_df['patient'].astype("<U64")
    res_df = res_df.merge(samples_df, on='patient', how='left')

    # add recurrence details
    recurrence_df = pd.read_csv(sample_recurrence_file)
    recurrence_df.columns = recurrence_df.columns.str.replace(" ", "_")
    recurrence_df['samples'] = recurrence_df['Samples'].str.split("_").str[0].str.lstrip("0")

    # common samples
    common_samples = recurrence_df['samples'][np.isin(recurrence_df['samples'].astype('<U64'), res_df['patient'].astype('<U64'))]

    recurrence_df = recurrence_df[recurrence_df['samples'].isin(common_samples)]

    # add recurrence details
    res_df = res_df.merge(recurrence_df, left_on='patient', right_on='samples', how='left')

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    file_regex = f"{results_dir}/*/*combined_VEP.ann.vcf.gz"
    files = glob.glob(file_regex)

    print(files)

    # process and combine all dfs
    res_df = load_and_process_dfs(files, annotated_variants_file, samples_description_file, sample_recurrence_file, cutoff, threads)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "samples_description_file=", "sample_recurrence_file=", "annotated_variants_file=", "cutoff=", "figure_data_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--samples_description_file"):
            samples_description_file = str(arg)
        if opt in ("--sample_recurrence_file"):
            sample_recurrence_file = str(arg)

        if opt in ("--annotated_variants_file"):
            annotated_variants_file = str(arg)
        if opt in ("--cutoff"):
            cutoff = float(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            
        if opt in ("--threads"):
            threads = int(arg)

    main()




