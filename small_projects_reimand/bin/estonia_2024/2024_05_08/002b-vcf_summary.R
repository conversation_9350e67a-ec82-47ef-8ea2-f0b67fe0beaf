# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





barplot_all = function(variant_type, input_df){


# set max
input_df$patient = fct_reorder(as.factor(input_df$patient), input_df$counts, sum, .desc=TRUE)

# plot raw expression
p = ggplot(input_df, aes(x = patient, y = log10(counts), fill = population_classification)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.8)) +  # position_dodge for side-by-side bars

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 6))



print(p)

if (variant_type != 'Indel'){
# plot raw expression
p = ggplot(input_df, aes(x = patient, y = log10(counts), fill = pathogenic_classification)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.8)) +  # position_dodge for side-by-side bars

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 6))


print(p)
}

return
}



barplot_somatic = function(variant_type, input_df){


input_df$patient = fct_reorder(as.factor(input_df$patient), input_df$counts, sum, .desc=TRUE)

if (variant_type != 'Indel'){
# do for population
sums <- input_df %>%
  group_by(patient, population_classification) %>%
  summarize(total_counts = sum(counts))


sums = as.data.frame(sums)

# set max
# sums$total_counts[sums$total_counts > max_variants] = max_variants
sums$patient = fct_reorder(as.factor(sums$patient), sums$total_counts, sum, .desc=TRUE)

# plot raw expression
p = ggplot(sums, aes(x = patient, y = log10(total_counts), fill = population_classification)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 8))


print(p)



input_df = input_df[input_df$pathogenic_classification == 'Likely damaging',]

# repeat for pathogenic
sums <- input_df %>%
  group_by(patient, pathogenic_classification) %>%
  summarize(total_counts = sum(counts))


sums = as.data.frame(sums)

# set max
# sums$total_counts[sums$total_counts > max_variants] = max_variants
sums$patient = fct_reorder(as.factor(sums$patient), sums$total_counts, sum, .desc=TRUE)

# plot raw expression
p = ggplot(sums, aes(x = patient, y = log10(total_counts), fill = pathogenic_classification)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 8))


print(p)
}
    
return()
}





# max_variants = 5000

pdf(opt$figure_file, width = 20)

# load df
df = read.csv(opt$figure_data_file, sep='\t')
df$population_classification = fct_reorder(as.factor(df$population_classification), df$counts, sum, .desc=FALSE)
df$pathogenic_classification = fct_reorder(as.factor(df$pathogenic_classification), df$counts, sum, .desc=FALSE)



for (variant_type in rev(unique(df$variant_type))){

main_df = df[df$variant_type == variant_type,]

# create plot
barplot_all(variant_type, main_df)

main_df = main_df[main_df$population_classification == 'Somatic',]
barplot_somatic(variant_type, main_df)

    
}




dev.off()


print(opt$figure_file)




