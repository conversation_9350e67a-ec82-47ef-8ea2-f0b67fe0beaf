# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np


help_message = '''
Failed
'''


def detailed_classification_of_variants(variant_types):
    # classify according to protein impact
    protein_impact = np.array(np.repeat('synonymous', len(variant_types)))
    protein_impact[variant_types.str.contains('missense|lost|prot', regex=True)] = 'missense'
    protein_impact[variant_types.str.contains('stop_gain', regex=True)] = 'stop_gain'
    protein_impact[variant_types.str.contains('inframe', regex=True)] = 'inframe_indel'
    protein_impact[variant_types.str.contains('frameshift', regex=True)] = 'frameshift'

    return protein_impact


def df_sums(df):
    # melt df
    df = df.groupby(['patient', 'population_classification', 'pathogenic_classification', 'variant_type', 'mutation_type', 'mutation_details', 'cgc_gene']).size().reset_index(name='counts')

    return df


def summarize_cgc_genes(df, cgc_file):
    # load cgc genes
    cgc = pd.read_csv(cgc_file, sep='\t')

    # intersect with cgc genes
    symbols = df['INFO'].str.split(";").str[-1]
    symbols = symbols.str.split("|").str[3]

    mask = symbols.isin(cgc['GENE_SYMBOL'])

    # add to df
    df['cgc_gene'] = ''
    df['is_cgc_gene'] = mask
    df.loc[mask, 'cgc_gene'] = symbols[mask]

    # subset to only these genes
    sub_df = df[mask]

    return df, sub_df


def load_and_classify(file, cgc_file):
    # load annotated variant file
    df = pd.read_csv(file, sep='\t')

    # classify by mutation type
    df['mutation_type'] = 'non-coding_region'

    # classify variant types
    variant_types = df['INFO'].str.split(";").str[-1]
    variant_types = variant_types.str.split("|").str[1]

    # classify by mutation type
    mask = pd.Series(variant_types).str.contains('frame|sense|synon|gain|lost|prot', regex=True)
    df.loc[mask, 'mutation_type'] = 'coding_region'

    # add details to each mutation
    df['mutation_details'] = detailed_classification_of_variants(variant_types)

    # summarize impacts with cgc genes
    df, cgc_only_df = summarize_cgc_genes(df, cgc_file)

    return df, cgc_only_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and classify by mutation type
    df, cgc_gene_df = load_and_classify(annotated_variant_file, cgc_file)

    # save to file
    cgc_gene_df.to_csv(cgc_only_results_file, sep='\t', index=False)
    df.to_csv(results_file, sep='\t', index=False)

    # sums of each mutation type
    df = df_sums(df)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["annotated_variant_file=", "cgc_file=", "results_file=", "cgc_only_results_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--annotated_variant_file"):
            annotated_variant_file = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)

        if opt in ("--results_file"):
            results_file = str(arg)
        if opt in ("--cgc_only_results_file"):
            cgc_only_results_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            
    main()




