# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def summarize_variants(figure_data_file):
    # read in data
    df = pd.read_csv(figure_data_file, sep='\t')

    # subset to non-synonymous mutations
    mask = ~np.isin(df['mutation_details'], ['synonymous', 'inframe_indel'])
    df = df[mask]

    # renmove population variants
    df = df[df['population_classification'] == 'Somatic']

    # summarize number of mutations in each gene
    res_df = df.groupby('cgc_gene')['patient'].nunique().reset_index()

    res_df = res_df.sort_values('patient', ascending=False)

    # Calculate fraction of patients
    total_patients = df['patient'].nunique()
    res_df['patient_fraction'] = res_df['patient'] / total_patients

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    res_df = summarize_variants(figure_data_file)

    # save to file
    res_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["figure_data_file=", "r_script=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


