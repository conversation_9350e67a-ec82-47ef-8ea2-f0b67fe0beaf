# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}







barplot = function(input_df){

# sort by patient number
input_df$cgc_gene = fct_reorder(input_df$cgc_gene, input_df$patient, .desc=TRUE)
    
# subset to the first 50 cgc_genes for plotting
input_df = input_df[1:50,]

p = ggplot(input_df, aes(x = cgc_gene, y = patient)) + plot_theme() +
geom_bar(stat = "identity") +
ggtitle('Somatic mutations in cohort') +
xlab("Gene") + ylab('Number of patients')

# Add labels for patient_fraction above the bars
p <- p + geom_text(aes(label = paste0(round(patient_fraction * 100), "%"),
                     y = patient + 0.5),  # Adjust vertical position of labels
                 size = 3, color = "black", vjust = 0.5, hjust=0, angle=90)  # Label properties
  
    
print(p)

return()
} 




pdf(opt$figure_file)

# load summary file
summary_df = read.csv(opt$figure_stats_file, sep='\t')

# create summary plot
barplot(summary_df)

dev.off()


print(opt$figure_file)

