# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(tidyr)
library(ComplexHeatmap)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



oncoprint_plot = function(input_df){
# exclude synonymous mutations
input_df = input_df[input_df$mutation_details != 'synonymous',]
input_df = input_df[input_df$mutation_details != 'inframe_in',]
    
# create matrix for oncoprint
# df <- input_df %>% mutate(mutation_info = paste(variant_type, mutation_details, sep = ";"))
df <- input_df %>% mutate(mutation_info = paste(mutation_details))

mutation_matrix <- df %>%
  group_by(cgc_gene, patient) %>%
  summarise(mutation_info = paste(unique(mutation_info), collapse = ";")) %>%
  pivot_wider(names_from = patient, values_from = mutation_info, values_fill = list(mutation_info = ""))

mutation_matrix <- as.data.frame(mutation_matrix)
rownames(mutation_matrix) <- mutation_matrix$cgc_gene
mutation_matrix$cgc_gene <- NULL
mutation_matrix <- as.matrix(mutation_matrix)


# Calculate mutation frequency for each gene
gene_mutation_frequency <- rowSums(mutation_matrix != "") / ncol(mutation_matrix)

# Filter out genes with mutation frequency less than 5%
mutation_matrix <- mutation_matrix[gene_mutation_frequency >= min_freq, ]

    

# define the oncoprint function
col = c(
  "SNV" = "red", 
  "missense" = "green", 
  "stop_gain" = "blue", 
  "inframe_in" = "purple", 
  "Indel" = "orange", 
  "frameshift" = "yellow"
)

alter_fun = list(
  SNV = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                      gp = gpar(fill = col["SNV"], col = NA)),
  missense = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                           gp = gpar(fill = col["missense"], col = NA)),
  stop_gain = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                            gp = gpar(fill = col["stop_gain"], col = NA)),
  inframe_in = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                             gp = gpar(fill = col["inframe_in"], col = NA)),
  Indel = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                        gp = gpar(fill = col["Indel"], col = NA)),
  frameshift = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                             gp = gpar(fill = col["frameshift"], col = NA))
)

# Sort rows (genes) by the total number of mutations
row_order <- order(rowSums(mutation_matrix != ""), decreasing = TRUE)

# Sort columns (samples) by the total number of mutations
column_order <- order(colSums(mutation_matrix != ""), decreasing = TRUE)

# Generate the OncoPrint
print(oncoPrint(mutation_matrix, 
          alter_fun = alter_fun, 
          col = col,
          row_order = row_order,
          column_order = column_order,
          column_title = "Mutation OncoPrint",
          heatmap_legend_param = list(title = "Mutations", 
                                      at = names(col), 
                                      labels = names(col))))

return()

}




pdf(opt$figure_file, width=20, height=20)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# subset to somatic, non-synonymous variants
input_df = input_df[input_df$mutation_details != 'synonymous',]
input_df = input_df[input_df$mutation_details != 'inframe_indel',]
input_df = input_df[input_df$population_classification == 'Somatic',]

min_freq = 0.05

# create oncoprint
oncoprint_plot(input_df)

dev.off()


print(opt$figure_file)

