# Alec Bahcheli
import argparse
import pandas as pd
import os
from pathlib import Path

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--new_results_dir", required=True)  # Directory containing new results (results_succinct)
    parser.add_argument("--old_results_dir", required=True)  # Directory containing old results (results_succinct_early_2025)
    parser.add_argument("--output_tsv", required=True)  # Output TSV file path
    return parser.parse_args()


def count_indels_in_vcf(vcf_path):
    """Count indels in a VCF file"""
    if not os.path.exists(vcf_path):
        return 0

    indel_count = 0

    try:
        with open(vcf_path, 'r') as f:
            for line in f:
                # Skip header lines
                if line.startswith('#'):
                    continue

                # Parse VCF line
                fields = line.strip().split('\t')
                if len(fields) < 5:
                    continue

                ref = fields[3]
                alt = fields[4]

                # Check if variant is an indel
                # Indel if reference and alternative lengths differ
                if len(ref) != len(alt):
                    indel_count += 1

    except Exception as e:
        print(f"Error processing {vcf_path}: {e}")
        return 0

    return indel_count


def find_vcf_files(directory):
    """Find all *-tumor_combined.vcf files in directory"""
    vcf_files = {}
    
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return vcf_files
    
    for file_path in Path(directory).glob("*-tumor_combined.vcf"):
        sample_name = file_path.stem.replace("-tumor_combined", "")
        vcf_files[sample_name] = str(file_path)
    
    return vcf_files


def compare_indel_counts(new_results_dir, old_results_dir):
    """Compare indel counts between two result directories"""
    
    # Find VCF files in both directories
    new_vcfs = find_vcf_files(new_results_dir)
    old_vcfs = find_vcf_files(old_results_dir)
    
    # Get all samples present in either directory
    all_samples = set(new_vcfs.keys()) | set(old_vcfs.keys())
    
    results = []
    
    for sample in sorted(all_samples):
        # Count indels in new results
        new_count = 0
        if sample in new_vcfs:
            new_count = count_indels_in_vcf(new_vcfs[sample])
        
        # Count indels in old results
        old_count = 0
        if sample in old_vcfs:
            old_count = count_indels_in_vcf(old_vcfs[sample])

        # Calculate difference (new - old)
        difference = new_count - old_count
        
        results.append({
            'sample': sample,
            'new_indels': new_count,
            'old_indels': old_count,
            'difference': difference
        })
    
    return pd.DataFrame(results)


def main():
    """Main function to compare indel counts"""
    args = parse_arguments()
    
    # Compare indel counts
    results_df = compare_indel_counts(args.new_results_dir, args.old_results_dir)
    
    # Save results
    results_df.to_csv(args.output_tsv, sep='\t', index=False)


if __name__ == "__main__":
    main()
