# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)

# options list for parser options
option_list <- list(
    make_option(c("-i","--input_tsv"), type="character", default=NULL,
            help="Input TSV file with indel comparison results",
            dest="input_tsv"),
    make_option(c("-o","--output_plot"), type="character", default=NULL,
            help="Output plot PDF file",
            dest="output_plot")
)

parser <- OptionParser(usage = "%prog -i input.tsv -o output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_indel_comparison_plots = function(data_df) {

    # 1. Scatter plot comparing old vs new indel counts
    p1 <- ggplot(data_df, aes(x = old_indels, y = new_indels)) +
        geom_point(size = 3, alpha = 0.7, color = "steelblue") +
        geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "red") +
        labs(title = "Indel Count Comparison: Old vs New Results", 
             x = "Old Indel Count", 
             y = "New Indel Count") +
        plot_theme()
    
    print(p1)
    
    # 2. Histogram of differences
    p2 <- ggplot(data_df, aes(x = difference)) +
        geom_histogram(binwidth = 1, alpha = 0.7, fill = "lightblue", color = "black") +
        geom_vline(xintercept = 0, linetype = "dashed", color = "red", size = 1) +
        scale_x_continuous(breaks = seq(floor(min(data_df$difference)),
                                        ceiling(max(data_df$difference)), 
                                        by = 1)) +
        labs(title = "Distribution of Indel Count Differences", 
             x = "Number of new indels", 
             y = "Number of Samples") +
        plot_theme()
    
    print(p2)
    
    # 3. Bar plot showing samples with largest differences
    top_changes <- data_df %>%
        arrange(desc(abs(difference))) %>%
        head(20)
    
    p3 <- ggplot(top_changes, aes(x = reorder(sample, difference), y = difference)) +
        geom_bar(stat = "identity", aes(fill = difference > 0), alpha = 0.8) +
        scale_fill_manual(values = c("TRUE" = "darkgreen", "FALSE" = "darkred"), 
                         name = "Change", labels = c("Increase", "Decrease")) +
        coord_flip() +
        labs(title = "Top 20 Samples with Largest Indel Count Changes", 
             x = "Sample", 
             y = "Difference (New - Old)") +
        plot_theme()
    
    print(p3)
    
    # 4. Summary statistics plot
    summary_stats <- data.frame(
        metric = c("Mean Difference", "Median Difference", "Samples Increased", "Samples Decreased"),
        value = c(mean(data_df$difference), 
                 median(data_df$difference),
                 sum(data_df$difference > 0),
                 sum(data_df$difference < 0))
    )
    
    p4 <- ggplot(summary_stats[1:2,], aes(x = metric, y = value)) +
        geom_bar(stat = "identity", fill = "lightcoral", alpha = 0.8, color = "black") +
        geom_text(aes(label = round(value, 2)), vjust = -0.5, size = 5) +
        labs(title = "Summary Statistics of Indel Count Changes", 
             x = "Metric", 
             y = "Value") +
        plot_theme() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1))
    
    print(p4)
    
    return()
}

pdf(opt$output_plot)

# Load data
data_df <- read.csv(opt$input_tsv, sep='\t')

# Create plots
create_indel_comparison_plots(data_df)

dev.off()
