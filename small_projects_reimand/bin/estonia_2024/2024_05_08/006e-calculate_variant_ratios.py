# Alec Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--indel_comparison", required=True)  # Indel comparison TSV file
    parser.add_argument("--snv_comparison", required=True)  # SNV comparison TSV file
    parser.add_argument("--output_tsv", required=True)  # Output TSV file path
    return parser.parse_args()


def calculate_ratios(indel_df, snv_df):
    """Calculate variant ratios for new and old results"""
    
    # Merge the dataframes on sample
    merged_df = pd.merge(indel_df, snv_df, on='sample', how='outer', suffixes=('_indel', '_snv'))
    
    # Fill NaN values with 0
    merged_df = merged_df.fillna(0)
    
    results = []
    
    for _, row in merged_df.iterrows():
        sample = row['sample']
        
        # New results
        new_indels = row['new_indels']
        new_snvs = row['new_snvs']
        new_total = new_indels + new_snvs
        
        # Old results
        old_indels = row['old_indels']
        old_snvs = row['old_snvs']
        old_total = old_indels + old_snvs
        
        # Calculate ratios for new results
        new_indel_snv_ratio = new_indels / new_snvs if new_snvs > 0 else 0
        new_snv_total_ratio = new_snvs / new_total if new_total > 0 else 0
        new_indel_total_ratio = new_indels / new_total if new_total > 0 else 0
        
        # Calculate ratios for old results
        old_indel_snv_ratio = old_indels / old_snvs if old_snvs > 0 else 0
        old_snv_total_ratio = old_snvs / old_total if old_total > 0 else 0
        old_indel_total_ratio = old_indels / old_total if old_total > 0 else 0
        
        # # Calculate ratio differences (new - old)
        # indel_snv_ratio_diff = new_indel_snv_ratio - old_indel_snv_ratio
        # snv_indel_ratio_diff = new_snv_indel_ratio - old_snv_indel_ratio
        # indel_total_ratio_diff = new_indel_total_ratio - old_indel_total_ratio
        
        results.append({
            'sample': sample,
            'new_indels': new_indels,
            'new_snvs': new_snvs,
            'new_total': new_total,
            'old_indels': old_indels,
            'old_snvs': old_snvs,
            'old_total': old_total,
            'new_indel_snv_ratio': new_indel_snv_ratio,
            'new_snv_indel_ratio': new_snv_total_ratio,
            'new_indel_total_ratio': new_indel_total_ratio,
            'old_indel_snv_ratio': old_indel_snv_ratio,
            'old_snv_indel_ratio': old_snv_total_ratio,
            'old_indel_total_ratio': old_indel_total_ratio
            # 'indel_snv_ratio_diff': indel_snv_ratio_diff,
            # 'snv_indel_ratio_diff': snv_indel_ratio_diff,
            # 'indel_total_ratio_diff': indel_total_ratio_diff
        })
    
    return pd.DataFrame(results)


def main():
    """Main function to calculate variant ratios"""
    args = parse_arguments()
    
    # Load comparison data
    indel_df = pd.read_csv(args.indel_comparison, sep='\t')
    snv_df = pd.read_csv(args.snv_comparison, sep='\t')
    
    # Calculate ratios
    ratios_df = calculate_ratios(indel_df, snv_df)
    
    # Save results
    ratios_df.to_csv(args.output_tsv, sep='\t', index=False)


if __name__ == "__main__":
    main()
