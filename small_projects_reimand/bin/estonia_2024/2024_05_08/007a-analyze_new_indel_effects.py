# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd
import os
from pathlib import Path

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--new_vcf_dir", required=True)  # Directory containing new annotated VCFs
    parser.add_argument("--old_vcf_dir", required=True)  # Directory containing old annotated VCFs
    parser.add_argument("--output_tsv", required=True)  # Output TSV file path
    return parser.parse_args()


def parse_vep_annotation(csq_string):
    """Parse VEP CSQ annotation string"""
    vep_fields = ['Allele', 'Consequence', 'IMPACT', 'SYMBOL', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON', 'HGVSc', 'HGVSp', 'cDNA_position', 'CDS_position', 'Protein_position', 'Amino_acids', 'Codons', 'Existing_variation', 'DISTANCE', 'STRAND', 'FLAGS', 'VARIANT_CLASS', 'SYMBOL_SOURCE', 'HGNC_ID', 'CANONICAL', 'MANE_SELECT', 'MANE_PLUS_CLINICAL', 'TSL', 'APPRIS', 'CCDS', 'ENSP', 'SWISSPROT', 'TREMBL', 'UNIPARC', 'UNIPROT_ISOFORM', 'GENE_PHENO', 'SIFT', 'PolyPhen', 'DOMAINS', 'miRNA', 'AF', 'AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'gnomADe_AF', 'gnomADe_AFR_AF', 'gnomADe_AMR_AF', 'gnomADe_ASJ_AF', 'gnomADe_EAS_AF', 'gnomADe_FIN_AF', 'gnomADe_NFE_AF', 'gnomADe_OTH_AF', 'gnomADe_SAS_AF', 'gnomADg_AF', 'gnomADg_AFR_AF', 'gnomADg_AMI_AF', 'gnomADg_AMR_AF', 'gnomADg_ASJ_AF', 'gnomADg_EAS_AF', 'gnomADg_FIN_AF', 'gnomADg_MID_AF', 'gnomADg_NFE_AF', 'gnomADg_OTH_AF', 'gnomADg_SAS_AF', 'MAX_AF', 'MAX_AF_POPS', 'FREQS', 'CLIN_SIG', 'SOMATIC', 'PHENO', 'PUBMED', 'MOTIF_NAME', 'MOTIF_POS', 'HIGH_INF_POS', 'MOTIF_SCORE_CHANGE', 'TRANSCRIPTION_FACTORS']
    
    annotations = []
    if 'CSQ=' in csq_string:
        csq_data = csq_string.split('CSQ=')[1].split(',')
        for annotation in csq_data:
            fields = annotation.split('|')
            if len(fields) >= len(vep_fields):
                ann_dict = dict(zip(vep_fields, fields))
                annotations.append(ann_dict)
    
    return annotations


def extract_indels_from_vcf(vcf_path):
    """Extract indels and their annotations from a VCF file"""
    indels = {}
    
    if not os.path.exists(vcf_path):
        return indels
    
    try:
        with open(vcf_path, 'r') as f:
            for line in f:
                if line.startswith('#'):
                    continue
                
                fields = line.strip().split('\t')
                if len(fields) < 8:
                    continue
                
                chrom, pos, _, ref, alt = fields[0], fields[1], fields[2], fields[3], fields[4]
                info_field = fields[7]
                
                # Check if variant is an indel
                if len(ref) != len(alt):
                    variant_key = f"{chrom}:{pos}:{ref}:{alt}"
                    
                    # Parse VEP annotations
                    annotations = parse_vep_annotation(info_field)
                    
                    indels[variant_key] = {
                        'chrom': chrom,
                        'pos': pos,
                        'ref': ref,
                        'alt': alt,
                        'annotations': annotations
                    }
                    
    except Exception as e:
        print(f"Error processing {vcf_path}: {e}")
    
    return indels


def find_vcf_files(directory, suffix="-filtered_VEP.vcf"):
    """Find all VCF files with specified suffix in directory"""
    vcf_files = {}
    
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return vcf_files
    
    for file_path in Path(directory).glob(f"*{suffix}"):
        sample_name = file_path.stem.replace(suffix.replace('.vcf', ''), '')
        vcf_files[sample_name] = str(file_path)
    
    return vcf_files


def analyze_new_indel_effects(new_vcf_dir, old_vcf_dir):
    """Analyze protein effects of new indels"""
    
    # Find VCF files in both directories
    new_vcfs = find_vcf_files(new_vcf_dir)
    old_vcfs = find_vcf_files(old_vcf_dir)
    
    # Get all samples present in both directories
    common_samples = set(new_vcfs.keys()) & set(old_vcfs.keys())
    
    results = []
    
    for sample in sorted(common_samples):        
        # Extract indels from both VCFs
        new_indels = extract_indels_from_vcf(new_vcfs[sample])
        old_indels = extract_indels_from_vcf(old_vcfs[sample])
        
        # Find indels that are only in the new VCF
        new_only_indels = set(new_indels.keys()) - set(old_indels.keys())
        
        # Analyze effects of new indels
        for variant_key in new_only_indels:
            indel_data = new_indels[variant_key]
            
            for annotation in indel_data['annotations']:
                # Include all annotations with gene symbols
                if annotation.get('SYMBOL'):
                    
                    results.append({
                        'sample': sample,
                        'variant_key': variant_key,
                        'chrom': indel_data['chrom'],
                        'pos': indel_data['pos'],
                        'ref': indel_data['ref'],
                        'alt': indel_data['alt'],
                        'gene_symbol': annotation.get('SYMBOL', ''),
                        'consequence': annotation.get('Consequence', ''),
                        'impact': annotation.get('IMPACT', ''),
                        'biotype': annotation.get('BIOTYPE', ''),
                        'hgvsc': annotation.get('HGVSc', ''),
                        'hgvsp': annotation.get('HGVSp', ''),
                        'protein_position': annotation.get('Protein_position', ''),
                        'amino_acids': annotation.get('Amino_acids', ''),
                        'canonical': annotation.get('CANONICAL', ''),
                        'mane_select': annotation.get('MANE_SELECT', ''),
                        'sift': annotation.get('SIFT', ''),
                        'polyphen': annotation.get('PolyPhen', ''),
                        'variant_class': annotation.get('VARIANT_CLASS', '')
                    })
    
    return pd.DataFrame(results)


def main():
    """Main function to analyze new indel effects"""
    args = parse_arguments()
    
    # Analyze new indel effects
    results_df = analyze_new_indel_effects(args.new_vcf_dir, args.old_vcf_dir)
    
    # Save results
    results_df.to_csv(args.output_tsv, sep='\t', index=False)
    
    print(f"Analysis complete. Found {len(results_df)} new indel effects across {results_df['sample'].nunique()} samples.")


if __name__ == "__main__":
    main()
