# <PERSON> Bahcheli
import argparse
import pandas as pd
import os
from pathlib import Path

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_dir", required=True)  # Directory containing per-sample TSV files
    parser.add_argument("--output_tsv", required=True)  # Output combined TSV file path
    return parser.parse_args()


def combine_sample_results(input_dir):
    """Combine per-sample indel effects results"""
    
    all_results = []
    
    # Find all TSV files in the input directory
    input_path = Path(input_dir)
    tsv_files = list(input_path.glob("*_new_indel_effects.tsv"))
    
    if not tsv_files:
        print(f"No TSV files found in {input_dir}")
        return pd.DataFrame()
    
    # Read and combine all TSV files
    for tsv_file in tsv_files:
        try:
            df = pd.read_csv(tsv_file, sep='\t')
            if len(df) > 0:
                all_results.append(df)
                print(f"Loaded {len(df)} effects from {tsv_file.name}")
            else:
                print(f"No effects found in {tsv_file.name}")
        except Exception as e:
            print(f"Error reading {tsv_file}: {e}")
    
    # Combine all results
    if all_results:
        combined_df = pd.concat(all_results, ignore_index=True)
        print(f"Combined {len(combined_df)} total effects from {len(all_results)} samples")
    else:
        # Create empty dataframe with expected columns
        combined_df = pd.DataFrame(columns=[
            'sample', 'variant_key', 'chrom', 'pos', 'ref', 'alt', 'gene_symbol',
            'consequence', 'impact', 'biotype', 'hgvsc', 'hgvsp', 'protein_position',
            'amino_acids', 'canonical', 'mane_select', 'sift', 'polyphen', 'variant_class'
        ])
        print("No effects found in any sample files")
    
    return combined_df


def main():
    """Main function to combine per-sample indel effects"""
    args = parse_arguments()
    
    # Combine sample results
    combined_df = combine_sample_results(args.input_dir)
    
    # Save combined results
    combined_df.to_csv(args.output_tsv, sep='\t', index=False)
    
    print(f"Combined results saved to {args.output_tsv}")
    print(f"Total effects: {len(combined_df)} across {combined_df['sample'].nunique() if len(combined_df) > 0 else 0} samples")


if __name__ == "__main__":
    main()
