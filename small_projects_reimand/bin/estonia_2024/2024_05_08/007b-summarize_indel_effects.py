# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd
import numpy as np
from collections import Counter

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_tsv", required=True)  # Input TSV with new indel effects
    parser.add_argument("--output_summary", required=True)  # Output summary TSV file
    parser.add_argument("--output_by_sample", required=True)  # Output by sample TSV file
    parser.add_argument("--output_by_gene", required=True)  # Output by gene TSV file
    return parser.parse_args()


def summarize_consequences(df):
    """Summarize consequences across all samples"""
    
    # Overall summary
    total_new_indels = len(df)
    total_samples = df['sample'].nunique()
    total_genes = df['gene_symbol'].nunique()
    
    # Consequence type counts
    consequence_counts = df['consequence'].value_counts().to_dict()
    
    # Impact level counts
    impact_counts = df['impact'].value_counts().to_dict()
    
    # Variant class counts
    variant_class_counts = df['variant_class'].value_counts().to_dict()
    
    # SIFT and PolyPhen predictions (for missense variants)
    sift_counts = df[df['sift'] != '']['sift'].value_counts().to_dict()
    polyphen_counts = df[df['polyphen'] != '']['polyphen'].value_counts().to_dict()
    
    # Canonical transcript counts
    canonical_counts = df['canonical'].value_counts().to_dict()
    
    # MANE Select counts
    mane_counts = df['mane_select'].value_counts().to_dict()
    
    summary_data = {
        'metric': [
            'total_new_indel_effects',
            'total_samples_with_new_indels',
            'total_genes_affected',
            'avg_new_indels_per_sample',
            'canonical_transcript_effects',
            'mane_select_effects'
        ],
        'value': [
            total_new_indels,
            total_samples,
            total_genes,
            round(total_new_indels / total_samples if total_samples > 0 else 0, 2),
            canonical_counts.get('YES', 0),
            mane_counts.get('YES', 0) + sum(v for k, v in mane_counts.items() if k.startswith('NM_'))
        ]
    }
    
    # Add consequence type counts
    for consequence, count in consequence_counts.items():
        summary_data['metric'].append(f'consequence_{consequence}')
        summary_data['value'].append(count)
    
    # Add impact level counts
    for impact, count in impact_counts.items():
        summary_data['metric'].append(f'impact_{impact}')
        summary_data['value'].append(count)
    
    return pd.DataFrame(summary_data)


def summarize_by_sample(df):
    """Summarize new indel effects by sample"""
    
    sample_summary = df.groupby('sample').agg({
        'variant_key': 'nunique',  # unique variants
        'gene_symbol': 'nunique',  # unique genes affected
        'consequence': lambda x: Counter(x).most_common(1)[0][0] if len(x) > 0 else '',  # most common consequence
        'impact': lambda x: Counter(x).most_common(1)[0][0] if len(x) > 0 else ''  # most common impact
    }).reset_index()
    
    sample_summary.columns = ['sample', 'new_indel_count', 'genes_affected', 'most_common_consequence', 'most_common_impact']
    
    # Add counts by impact level
    impact_by_sample = df.groupby(['sample', 'impact']).size().unstack(fill_value=0)
    sample_summary = sample_summary.merge(impact_by_sample, left_on='sample', right_index=True, how='left')
    
    # Add counts of canonical and MANE select effects
    canonical_counts = df[df['canonical'] == 'YES'].groupby('sample').size().reset_index(name='canonical_effects')
    sample_summary = sample_summary.merge(canonical_counts, on='sample', how='left')
    sample_summary['canonical_effects'] = sample_summary['canonical_effects'].fillna(0)
    
    mane_counts = df[df['mane_select'].str.startswith('NM_', na=False) | (df['mane_select'] == 'YES')].groupby('sample').size().reset_index(name='mane_select_effects')
    sample_summary = sample_summary.merge(mane_counts, on='sample', how='left')
    sample_summary['mane_select_effects'] = sample_summary['mane_select_effects'].fillna(0)
    
    return sample_summary


def summarize_by_gene(df):
    """Summarize new indel effects by gene"""
    
    gene_summary = df.groupby('gene_symbol').agg({
        'sample': 'nunique',  # number of samples with new indels in this gene
        'variant_key': 'nunique',  # unique variants in this gene
        'consequence': lambda x: Counter(x).most_common(1)[0][0] if len(x) > 0 else '',  # most common consequence
        'impact': lambda x: Counter(x).most_common(1)[0][0] if len(x) > 0 else '',  # most common impact
        'canonical': lambda x: sum(1 for v in x if v == 'YES'),  # canonical transcript effects
        'mane_select': lambda x: sum(1 for v in x if v.startswith('NM_') or v == 'YES')  # MANE select effects
    }).reset_index()
    
    gene_summary.columns = ['gene_symbol', 'samples_affected', 'unique_variants', 'most_common_consequence', 'most_common_impact', 'canonical_effects', 'mane_select_effects']
    
    # Sort by number of samples affected (descending)
    gene_summary = gene_summary.sort_values('samples_affected', ascending=False)
    
    # Add impact level counts
    impact_by_gene = df.groupby(['gene_symbol', 'impact']).size().unstack(fill_value=0)
    gene_summary = gene_summary.merge(impact_by_gene, left_on='gene_symbol', right_index=True, how='left')
    
    return gene_summary


def main():
    """Main function to summarize indel effects"""
    args = parse_arguments()
    
    # Load data
    df = pd.read_csv(args.input_tsv, sep='\t')
    
    if len(df) == 0:
        print("No new indel effects found.")
        # Create empty summary files
        pd.DataFrame({'metric': ['no_new_indels'], 'value': [0]}).to_csv(args.output_summary, sep='\t', index=False)
        pd.DataFrame().to_csv(args.output_by_sample, sep='\t', index=False)
        pd.DataFrame().to_csv(args.output_by_gene, sep='\t', index=False)
        return
    
    # Generate summaries
    overall_summary = summarize_consequences(df)
    sample_summary = summarize_by_sample(df)
    gene_summary = summarize_by_gene(df)
    
    # Save results
    overall_summary.to_csv(args.output_summary, sep='\t', index=False)
    sample_summary.to_csv(args.output_by_sample, sep='\t', index=False)
    gene_summary.to_csv(args.output_by_gene, sep='\t', index=False)
    
    print(f"Summary complete:")
    print(f"  - Overall summary: {len(overall_summary)} metrics")
    print(f"  - Sample summary: {len(sample_summary)} samples")
    print(f"  - Gene summary: {len(gene_summary)} genes")


if __name__ == "__main__":
    main()
