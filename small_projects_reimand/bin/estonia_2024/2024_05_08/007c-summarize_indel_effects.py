# <PERSON>cheli
import argparse
import pandas as pd

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_tsv", required=True)  # Input TSV with new indel effects
    parser.add_argument("--output_simple", required=True)  # Simple output TSV for visualization
    return parser.parse_args()


def classify_protein_coding(biotype, consequence):
    """Classify mutation as protein-coding or non-protein-coding"""
    # Define protein-coding consequences
    protein_coding_consequences = {
        'missense_variant', 'nonsense_variant', 'frameshift_variant',
        'inframe_insertion', 'inframe_deletion', 'start_lost', 'stop_lost',
        'stop_gained', 'start_retained_variant', 'stop_retained_variant',
        'synonymous_variant', 'coding_sequence_variant'
    }

    # Check if biotype is protein_coding and consequence affects protein
    if biotype == 'protein_coding':
        # Check if any of the consequences (can be multiple separated by &) are protein-coding
        consequence_list = consequence.split('&') if consequence else []
        for cons in consequence_list:
            if cons.strip() in protein_coding_consequences:
                return 'protein_coding'

    return 'non_protein_coding'


def create_simple_summary(infile):
    """Create simple summary for visualization"""

    # Load data
    df = pd.read_csv(infile, sep='\t')

    # Classify each mutation
    df['mutation_type'] = df.apply(lambda row: classify_protein_coding(row['biotype'], row['consequence']), axis=1)

    # Count by sample and mutation type
    summary = df.groupby(['sample', 'mutation_type']).size().reset_index(name='count')

    # Pivot to have protein_coding and non_protein_coding as columns
    summary_pivot = summary.pivot(index='sample', columns='mutation_type', values='count').fillna(0).reset_index()

    # Ensure both columns exist
    if 'protein_coding' not in summary_pivot.columns:
        summary_pivot['protein_coding'] = 0
    if 'non_protein_coding' not in summary_pivot.columns:
        summary_pivot['non_protein_coding'] = 0

    # Reorder columns
    summary_pivot = summary_pivot[['sample', 'protein_coding', 'non_protein_coding']]

    return summary_pivot


def main():
    """Main function to summarize indel effects"""
    args = parse_arguments()

    # Generate simple summary
    simple_summary = create_simple_summary(args.input_tsv)

    # Save results
    simple_summary.to_csv(args.output_simple, sep='\t', index=False)


if __name__ == "__main__":
    main()
