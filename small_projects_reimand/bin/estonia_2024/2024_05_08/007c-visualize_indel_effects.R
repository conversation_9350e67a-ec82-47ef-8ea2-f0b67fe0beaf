# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-i","--input_tsv"), type="character", default=NULL,
            help="Simple summary TSV file with protein-coding classification",
            dest="input_tsv"),
    make_option(c("-o","--output_plot"), type="character", default=NULL,
            help="Output plot PDF file",
            dest="output_plot")
)

parser <- OptionParser(usage = "%prog -i input.tsv -o output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_simple_indel_plot = function(data_df) {

    if (nrow(data_df) == 0) {
        # Create empty plot if no data
        p <- ggplot() +
            annotate("text", x = 0.5, y = 0.5, label = "No new indel effects found",
                    hjust = 0.5, vjust = 0.5, size = 8) +
            xlim(0, 1) + ylim(0, 1) +
            labs(title = "New Indel Effects Classification") +
            theme_void() +
            theme(plot.title = element_text(size = 20, hjust = 0.5))

        print(p)
        return()
    }

    # Reshape data for plotting
    plot_data <- data_df %>%
        pivot_longer(cols = c(protein_coding, non_protein_coding),
                     names_to = "mutation_type",
                     values_to = "count") %>%
        mutate(mutation_type = case_when(
            mutation_type == "protein_coding" ~ "Protein-Coding",
            mutation_type == "non_protein_coding" ~ "Non-Protein-Coding"
        ))

    # Create grouped bar plot
    p1 <- ggplot(plot_data, aes(x = sample, y = count, fill = mutation_type)) +
        geom_bar(stat = "identity", position = "dodge", alpha = 0.8) +
        scale_fill_manual(values = c("Protein-Coding" = "darkblue",
                                   "Non-Protein-Coding" = "lightcoral"),
                         name = "Mutation Type") +
        labs(title = "New Indel Effects by Sample",
             subtitle = "Classification: Protein-Coding vs Non-Protein-Coding",
             x = "Sample",
             y = "Number of New Indel Effects") +
        plot_theme() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1))

    print(p1)

    # Create summary plot showing totals
    summary_data <- plot_data %>%
        group_by(mutation_type) %>%
        summarise(total_count = sum(count), .groups = 'drop')

    p2 <- ggplot(summary_data, aes(x = mutation_type, y = total_count, fill = mutation_type)) +
        geom_bar(stat = "identity", alpha = 0.8) +
        geom_text(aes(label = total_count), vjust = -0.5, size = 6) +
        scale_fill_manual(values = c("Protein-Coding" = "darkblue",
                                   "Non-Protein-Coding" = "lightcoral")) +
        labs(title = "Total New Indel Effects by Type",
             x = "Mutation Type",
             y = "Total Count") +
        plot_theme() +
        theme(legend.position = "none")

    print(p2)

    return()
}

pdf(opt$output_plot, width = 12, height = 8)

# Load data
data_df <- if (!is.null(opt$input_tsv) && file.exists(opt$input_tsv)) {
    read.csv(opt$input_tsv, sep='\t')
} else {
    data.frame()
}

# Create plots
create_simple_indel_plot(data_df)

dev.off()
