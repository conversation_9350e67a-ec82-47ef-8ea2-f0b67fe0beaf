# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-s","--summary_tsv"), type="character", default=NULL,
            help="Overall summary TSV file",
            dest="summary_tsv"),
    make_option(c("-a","--sample_tsv"), type="character", default=NULL,
            help="Sample summary TSV file",
            dest="sample_tsv"),
    make_option(c("-g","--gene_tsv"), type="character", default=NULL,
            help="Gene summary TSV file",
            dest="gene_tsv"),
    make_option(c("-o","--output_plot"), type="character", default=NULL,
            help="Output plot PDF file",
            dest="output_plot")
)

parser <- OptionParser(usage = "%prog -s summary.tsv -a sample.tsv -g gene.tsv -o output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_indel_effects_plots = function(summary_df, sample_df, gene_df) {

    # 1. Overall summary bar plot
    if (nrow(summary_df) > 0) {
        # Filter for main metrics
        main_metrics <- summary_df %>%
            filter(grepl("^(total_|avg_|canonical_|mane_)", metric))
        
        if (nrow(main_metrics) > 0) {
            p1 <- ggplot(main_metrics, aes(x = reorder(metric, value), y = value)) +
                geom_bar(stat = "identity", fill = "steelblue", alpha = 0.8) +
                geom_text(aes(label = value), hjust = -0.1, size = 4) +
                coord_flip() +
                labs(title = "Overall Summary of New Indel Effects", 
                     x = "Metric", 
                     y = "Count") +
                plot_theme()
            
            print(p1)
        }
        
        # 2. Consequence types
        consequence_metrics <- summary_df %>%
            filter(grepl("^consequence_", metric)) %>%
            mutate(consequence = gsub("consequence_", "", metric))
        
        if (nrow(consequence_metrics) > 0) {
            p2 <- ggplot(consequence_metrics, aes(x = reorder(consequence, value), y = value)) +
                geom_bar(stat = "identity", fill = "darkgreen", alpha = 0.8) +
                geom_text(aes(label = value), hjust = -0.1, size = 4) +
                coord_flip() +
                labs(title = "Distribution of Consequence Types", 
                     x = "Consequence Type", 
                     y = "Count") +
                plot_theme()
            
            print(p2)
        }
        
        # 3. Impact levels
        impact_metrics <- summary_df %>%
            filter(grepl("^impact_", metric)) %>%
            mutate(impact = gsub("impact_", "", metric))
        
        if (nrow(impact_metrics) > 0) {
            p3 <- ggplot(impact_metrics, aes(x = reorder(impact, value), y = value)) +
                geom_bar(stat = "identity", fill = "darkorange", alpha = 0.8) +
                geom_text(aes(label = value), hjust = -0.1, size = 4) +
                coord_flip() +
                labs(title = "Distribution of Impact Levels", 
                     x = "Impact Level", 
                     y = "Count") +
                plot_theme()
            
            print(p3)
        }
    }
    
    # 4. Sample-level analysis
    if (nrow(sample_df) > 0) {
        # New indels per sample
        p4 <- ggplot(sample_df, aes(x = reorder(sample, new_indel_count), y = new_indel_count)) +
            geom_bar(stat = "identity", fill = "purple", alpha = 0.8) +
            coord_flip() +
            labs(title = "New Indel Effects per Sample", 
                 x = "Sample", 
                 y = "Number of New Indel Effects") +
            plot_theme()
        
        print(p4)
        
        # Genes affected per sample
        p5 <- ggplot(sample_df, aes(x = reorder(sample, genes_affected), y = genes_affected)) +
            geom_bar(stat = "identity", fill = "darkred", alpha = 0.8) +
            coord_flip() +
            labs(title = "Genes Affected by New Indels per Sample", 
                 x = "Sample", 
                 y = "Number of Genes Affected") +
            plot_theme()
        
        print(p5)
        
        # Impact distribution by sample (if impact columns exist)
        impact_cols <- names(sample_df)[grepl("^(HIGH|MODERATE|LOW|MODIFIER)$", names(sample_df))]
        if (length(impact_cols) > 0) {
            sample_impact <- sample_df %>%
                select(sample, all_of(impact_cols)) %>%
                pivot_longer(cols = -sample, names_to = "impact", values_to = "count") %>%
                filter(count > 0)
            
            if (nrow(sample_impact) > 0) {
                p6 <- ggplot(sample_impact, aes(x = sample, y = count, fill = impact)) +
                    geom_bar(stat = "identity", position = "stack") +
                    scale_fill_manual(values = c("HIGH" = "red", "MODERATE" = "orange", 
                                               "LOW" = "yellow", "MODIFIER" = "lightblue")) +
                    coord_flip() +
                    labs(title = "Impact Distribution by Sample", 
                         x = "Sample", 
                         y = "Number of Effects",
                         fill = "Impact Level") +
                    plot_theme()
                
                print(p6)
            }
        }
    }
    
    # 5. Gene-level analysis
    if (nrow(gene_df) > 0) {
        # Top genes affected (limit to top 20)
        top_genes <- gene_df %>%
            head(20)
        
        p7 <- ggplot(top_genes, aes(x = reorder(gene_symbol, samples_affected), y = samples_affected)) +
            geom_bar(stat = "identity", fill = "darkblue", alpha = 0.8) +
            geom_text(aes(label = samples_affected), hjust = -0.1, size = 3) +
            coord_flip() +
            labs(title = "Top 20 Genes Most Affected by New Indels", 
                 x = "Gene Symbol", 
                 y = "Number of Samples Affected") +
            plot_theme()
        
        print(p7)
        
        # Unique variants per gene (top 20)
        p8 <- ggplot(top_genes, aes(x = reorder(gene_symbol, unique_variants), y = unique_variants)) +
            geom_bar(stat = "identity", fill = "darkgreen", alpha = 0.8) +
            geom_text(aes(label = unique_variants), hjust = -0.1, size = 3) +
            coord_flip() +
            labs(title = "Top 20 Genes with Most Unique New Indel Variants", 
                 x = "Gene Symbol", 
                 y = "Number of Unique Variants") +
            plot_theme()
        
        print(p8)
    }
    
    return()
}

pdf(opt$output_plot, width = 14, height = 10)

# Load data
summary_df <- if (!is.null(opt$summary_tsv) && file.exists(opt$summary_tsv)) {
    read.csv(opt$summary_tsv, sep='\t')
} else {
    data.frame()
}

sample_df <- if (!is.null(opt$sample_tsv) && file.exists(opt$sample_tsv)) {
    read.csv(opt$sample_tsv, sep='\t')
} else {
    data.frame()
}

gene_df <- if (!is.null(opt$gene_tsv) && file.exists(opt$gene_tsv)) {
    read.csv(opt$gene_tsv, sep='\t')
} else {
    data.frame()
}

# Create plots
create_indel_effects_plots(summary_df, sample_df, gene_df)

dev.off()
