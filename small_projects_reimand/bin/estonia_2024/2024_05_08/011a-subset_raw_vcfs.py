# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import gzip
# import pysam

help_message = '''
Failed
'''



def remove_vcf_duplicates(input_vcf_gz, output_vcf_gz):
    seen = set()

    with gzip.open(input_vcf_gz, 'rt') as infile, gzip.open(output_vcf_gz, 'wt') as outfile:
        for line in infile:
            if line.startswith("#"):
                # Write headers directly
                outfile.write(line)
            else:
                fields = line.strip().split('\t')
                if len(fields) < 5:
                    continue  # skip malformed lines
                key = (fields[0], fields[1], fields[3], fields[4])  # CHROM, POS, REF, ALT
                if key not in seen:
                    seen.add(key)
                    outfile.write(line)


def load_and_process_dfs(input_file, filtered_vcf_file):
    # filter the VCF file to remove duplicate variants
    remove_vcf_duplicates(input_file, filtered_vcf_file)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    df = load_and_process_dfs(vcf_consensus_file, filtered_vcf_file)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["vcf_consensus_file=", "filtered_vcf_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--vcf_consensus_file"):
            vcf_consensus_file = str(arg)

        if opt in ("--filtered_vcf_file"):
            filtered_vcf_file = str(arg)

    main()




