# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''

def remove_vcf_duplicates(input_vcf, output_vcf):
    variant_dict = {}
    with open(input_vcf, 'r') as infile, open(output_vcf, 'w') as outfile:
        for line in infile:
            if line.startswith("#"):
                # Write headers directly
                outfile.write(line)
            else:
                fields = line.strip().split('\t')
                # if fields[6] == 'PASS':
                if len(fields) < 5:
                    print(f"Error: {line}")  # skip malformed lines
                    continue
                # check if the variant is already in the dictionary
                variant = (fields[0], fields[1], fields[3], fields[4])
    
                # # check if it's an indel
                # if len(fields[3]) > 1 or len(fields[4]) > 1:
                #     print(variant)
                #     print(variant in variant_dict)
                
                if variant not in variant_dict:
                    outfile.write(line)
                    # add the variant to the dictionary
                    variant_dict[variant] = True
    

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # remove duplicates from the vcf file
    remove_vcf_duplicates(merged_vcf, succinct_vcf)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["merged_vcf=", "succinct_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--merged_vcf"):
            merged_vcf = str(arg)

        if opt in ("--succinct_vcf"):
            succinct_vcf = str(arg)

    main()



