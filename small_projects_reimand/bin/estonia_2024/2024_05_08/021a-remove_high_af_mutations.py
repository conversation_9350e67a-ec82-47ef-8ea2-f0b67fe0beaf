# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import gzip

help_message = '''
Failed
'''

def filter_by_max_af(input_vcf, output_vcf, max_af = 0.01, max_af_index=66):
    with gzip.open(input_vcf, 'rt') as infile, open(output_vcf, 'w') as outfile:
        for line in infile:
            if line.startswith("#"):
                # Write headers directly
                outfile.write(line)
                continue
            fields = line.strip().split('\t')
            if len(fields) < 5:
                print(f"Error: {line}")  # skip malformed lines
                continue
            info = fields[7]
            # Use only the first allele annotation (most relevant or canonical)
            max_af_str = info.split("CSQ=")[1].split('|')[max_af_index]
            max_af_val = float(max_af_str) if max_af_str else 0.0
            if max_af_val <= max_af:
                outfile.write(line)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # filter by allele frequency
    filter_by_max_af(annotated_vcf, filtered_vcf)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["annotated_vcf=", "filtered_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--annotated_vcf"):
            annotated_vcf = str(arg)

        if opt in ("--filtered_vcf"):
            filtered_vcf = str(arg)

    main()



