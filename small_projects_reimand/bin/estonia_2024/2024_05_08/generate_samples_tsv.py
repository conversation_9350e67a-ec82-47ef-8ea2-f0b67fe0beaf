main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek'
files = ['sarek_decrypted-data-DNA-ENC.csv', 'sarek_PERH2-BAMS-ENC_p1.csv', 'sarek_PERH2-BAMS-ENC_p2.csv', 'sarek_PERH_data-BAMS-ENC_p1.csv', 'sarek_PERH_data-BAMS-ENC_p2.csv', 'zsample.csv']


# Initialize an empty list to store the first columns
first_columns = []

res_df = []

# Loop through each file
for file in files:
    # Load the DataFrame from the file
    df = pd.read_csv(os.path.join(main_dir, file))
    
    # Assuming the first column name is 'column_name'
    first_column = df.iloc[:, 0].tolist()  # Extract the first column values as a list
    
    # Add the first column values to the list
    first_columns.extend(first_column)

    # add source details
    if file == 'zsample.csv':
        df['source'] = 'PERH_data-BAMS-ENC'
    else:
        df['source'] = "_".join(file.split(".")[0].split("_")[1:])

    res_df.append(df[['patient', 'source']])

first_columns = np.unique(first_columns)

# print('", "'.join(first_columns))
print(len(first_columns))

res_df = pd.concat(res_df)
mask = ~res_df['patient'].duplicated()
res_df = res_df.loc[mask,:]

res_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/samples.tsv", sep='\t', index=False)
res_df
