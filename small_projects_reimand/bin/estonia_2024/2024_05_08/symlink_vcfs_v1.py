

main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08'

regex_patterns = ['mutect2.filtered.vcf.gz', 'strelka.variants.vcf.gz', 'freebayes.vcf.gz']
dir_list = ['mutect2_raw', 'strelka_raw', 'freebayes_raw']
destination_dir_list = [os.path.join(main_dir, 'all_vcfs', x) for x in dir_list]



samples = []

for i, regex_pattern in enumerate(regex_patterns):
    # Define the source directory
    dir_list = ['results-nf_decrypted-data-DNA-ENC', 'results-nf_PERH2-BAMS-ENC', 'results-nf_PERH_data-BAMS-ENC', 'results-zsample']
    source_dirs = [os.path.join(main_dir, x) for x in dir_list]

    if not os.path.exists(destination_dir_list[i]):
        os.makedirs(destination_dir_list[i])
    
    # Function to create symbolic links for files matching the regex pattern
    def create_symbolic_links(source_dir):
        # Search for files matching the regex pattern in the source directory
        files = glob.glob(os.path.join(source_dir, '**', '**', '*'), recursive=True)
        matching_files = [file for file in files if file.endswith(regex_pattern)]
        matching_files.extend([file for file in files if file.endswith(regex_pattern + ".tbi")])

        samples.extend(pd.Series(matching_files).str.split("/").str[-2].str.split("-").str[0])
        
        # Create symbolic links for matching files in the destination directory
        for file_path in matching_files:
            file_name = os.path.basename(file_path)
            destination_link = os.path.join(destination_dir_list[i], file_name)
            if not os.path.exists(destination_link):
                os.symlink(file_path, destination_link)
                # print(f"Created symbolic link for {file_name}")
            # else:
            #     print(f"Symbolic link for {file_name} already exists in {destination_dir}")
    
    # Iterate over each source directory and create symbolic links
    for source_dir in source_dirs:
        create_symbolic_links(source_dir)


