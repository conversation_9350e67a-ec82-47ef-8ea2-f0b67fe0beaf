# Alec Bahcheli

# run version (typically date)
VERSION='estonia_2024/2024_05_08'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", VERSION, "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", VERSION, "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'

chromosome_list = ['chr' + str(i) for i in range(23)][1:]
chromosome_list.extend(['chrX', 'chrY'])


sample_runs = ['nf_PERH2-BAMS-ENC_p1', 'nf_PERH2-BAMS-ENC_p2', 'nf_PERH_data-BAMS-ENC_p1', 'nf_PERH_data-BAMS-ENC_p2', 'nf_decrypted-data-DNA-ENC']

samples = ["1008", "1009", "1058", "106", "107", "108", "115", "1284", "146", "153", "157", "158", "15936", "257", "294", "300", "301", "302", "303", "304", "305", "343", "344", "354", "363", "364", "384", "396", "397", "410482", "410487", "415218", "427", "45090", "518400", "54969", "56185", "58004", "58669", "611715", "612509", "615164", "61549", "618466", "618925", "619189", "63242", "64224", "64647", "64815", "67639", "69393", "710223", "711005", "711305", "711441", "712599", "718976", "72017", "73310", "74306", "749", "750", "79228", "79856", "805595", "811989", "816174", "817344", "818318", "859", "860", "892", "901289", "901826", "904684", "905", "905344", "906", "91", "916285", "918500", "927", "94", "941", "IVKH", "M1207987", "M1308464", "M1400519", "M1402871", "M1502315", "M201101474", "M201102498", "M201109265", "M201109514", "M201200852", "M201201464", "M201202994", "M201207597", "M201208496", "M201301448", "M201301639", "M201302364", "M201305657", "M201305817", "M201305818", "M201305986", "M201307747", "M201308134", "M201309141", "M201309783", "M201400218", "M201400880", "M201401097", "M201401285", "M201401950", "M201402017", "M201402485", "M201402870", "M201403436", "M201403572", "M201403672", "M201404036", "M201404144", "M201404940", "M201405126", "M201405277", "M201405596", "M201406061", "M201406184", "M201500596", "M201500632", "M201501276", "M201501320", "M201501554", "M201501652", "M201501653", "M201502042_A", "M201502042_B", "M201503294", "M201504055", "M201504156", "M201504347", "M201505317", "M201505598", "M201505599", "M201506208", "M201506834", "M201507012", "M201507466", "M201507993", "M201508410", "M201508988", "MZZ0Z755", "O201009049", "O201012423", "O201012924", "P1400242", "P1403053", "P1409332", "P201103401", "P201103547", "P201110327", "P201110566", "P201209910", "P201300967", "P201303028", "P201303501", "P201305237", "P201306082", "P201306312", "P201306634", "P201306887", "P201309335", "P201310059", "P201400025", "P201402751", "P201406539", "P201406675", "P201409588", "P201409797", "P201413005", "P201500190", "P201500292", "P201501442", "P201504782", "P201509880", "X201600572", "X201602143", "X201602525", "X201605202", "X201605586", "X201605838", "X201609690", "X201610295", "X201610732", "X201610893_1", "X201610893_2", "X201612680", "X201614374", "X201617428", "X201617706", "X201700179", "X201701399", "X201701413", "X201702805", "X201721754", "X201800915", "X201916389", "ZZZ04911"]


cutoffs = ['0.1', '0.05', '0.01', '0.001']

###################################
# Complete workflow
###################################


# define the objective (make the output files)
rule all:
    input:
        expand("{res_dir}/all_vcfs/results_succinct/{sample}-tumor_combined.vcf", res_dir = RES_DIR, sample = samples),
        expand("{res_dir}/all_vcfs/results_succinct/{sample}-tumor_combined.vcf.gz.tbi", res_dir = RES_DIR, sample = samples),

        expand("{res_dir}/all_vcfs/results/{sample}-tumor_combined.vcf.gz.tbi", res_dir = RES_DIR, sample = samples),
        expand("{res_dir}/all_vcfs/finalized_annotated/{sample}-tumor_snpEFF_VEP.tsv", res_dir = RES_DIR, sample = samples),
        expand("{res_dir}/all_vcfs/finalized_unannotated_vcfs/{sample}-tumor.vcf.gz", res_dir = RES_DIR, sample = samples)
        # RES_DIR + "/analysis_sarek/sigprofiler/JOB_METADATA_SPA.txt",
        # expand("{res_dir}/all_vcfs/filtered_annotations_af/{sample}-filtered_VEP.vcf", res_dir = RES_DIR, sample = samples),




        # expand("{res_dir}/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz", res_dir = RES_DIR, sample = samples)

        # expand("{res_dir}/all_vcfs/results/{sample}-tumor_combined.vcf", res_dir = RES_DIR, sample = samples)

        # merged_vcf = RES_DIR + "/all_vcfs/results/{sample}-tumor_combined.vcf"



        # RES_DIR + "/_figures/001-vcf_summary.pdf",
        # FIGURE_DIR + "/001-unique_vcf_summary.pdf",

        # expand("{res_dir}/_figures/002-annotated_summary_{cutoff}.pdf", res_dir = RES_DIR, cutoff = cutoffs),
        # RES_DIR + "/_figures/002-summary_classify_protein_impact.pdf",
        # RES_DIR + "/_figures/002-summarize_cgc_impact.pdf",
        # tmp = RES_DIR + "/_figures/002-oncoprint_cgc_genes.pdf"





# create main project directories
rule main_directories:        
    output:
        data = DATA_DIR + "/null.txt",
        raw_data = RAW_DATA_DIR + "/null.txt",
        ref_data = REF_DATA_DIR + "/null.txt",
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR} {DATA_DIR} {RAW_DATA_DIR} {REF_DATA_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures} {output.data} {output.raw_data} {output.ref_data}")




#####################
# post-processing the Sarek pipeline
#####################
# include: "snakemake/001-variant_calling.smk"
include: "snakemake/002-sarek_post_processing.smk"
# include: "snakemake/003-vcf_visualize_summary.smk"

# process the annotated files
# include: "snakemake/004-post_process_annotated_vcfs.smk"

include: "snakemake/005-filtering_and_annotating.smk"





