#!/bin/bash
#$ -P reimandlab
#$ -N sarek_PERH2-BAMS-ENC_variants
#$ -l h_vmem=20G,h_rt=14:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/estonia_2024/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/estonia_2024/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/nf_PERH2-BAMS-ENC_variants

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-sarek_3.4.2/3_4_2 \
--input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek_vc/sarek_PERH2-BAMS-ENC_p2_v2.csv \
--outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/nf_PERH2-BAMS-ENC_variants \
-profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek/nextflow.config \
--tools 'freebayes,mutect2,strelka' \
--genome /.mounts/labs/reimandlab/private/users/abahcheli/tmp/tmp/hg38.fa \
--step variant_calling \
-resume


