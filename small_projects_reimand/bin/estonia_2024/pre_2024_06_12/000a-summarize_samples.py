# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''

def list_files_dir1(directory):
    # List all files in the directory
    files = [file for file in os.listdir(directory) if np.logical_and("bam" in file, np.invert("bai" in file or "md5sum" in file))]

    # df to be
    top_files_df = []

    # lists of files
    sorted_samples = []


    # iteratively copy the most processed files
    for file in files:
        # file info
        n_underscore = file.count("_")
        if n_underscore > 1:
            sample = "_".join([s for s in file.split("_")[:-1] if len(s) > 0]).split(".")[-1]
            # print(file, sample)
        else:
            sample = file.split("_")[0].split(".")[-1]
        patient = sample.split("_")[0]
        file_path = os.path.join(directory, file)
        sample_type = file.split("_")[-1].split(".")[0]

        # if the file is sorted then add it
        if file.startswith("sorted"):
            sorted_samples.append(sample)
            top_files_df.append([sample, patient, file_path, sample_type])


    # create df
    top_files_df = pd.DataFrame(top_files_df, columns = ['sample', 'patient', 'file_path', 'sample_type'])
    new_df = []

    # add the files that were not sorted if not the same sample as sorted
    for file in files:
        # make sure not sorted
        if not file.startswith("sorted"):
            n_underscore = file.count("_")
            if n_underscore > 1:
                sample = "_".join([s for s in file.split("_")[:-1] if len(s) > 0]).split(".")[-1]
                # print(file, sample)
            else:
                sample = file.split("_")[0].split(".")[-1]
            patient = sample.split("_")[0]
            file_path = os.path.join(directory, file)
            sample_type = file.split("_")[-1].split(".")[0]

            # check to make sure the files are in the df
            if not np.isin(sample, top_files_df['sample']):
                new_df.append([sample, patient, file_path, sample_type])


    new_df = pd.DataFrame(new_df, columns = ['sample', 'patient', 'file_path', 'sample_type'])
    top_files_df = pd.concat([top_files_df, new_df])

    return top_files_df



def list_files_dir2(top_files_df, directory):
    # List all files in the directory
    files = [file for file in os.listdir(directory) if np.logical_and("bam" in file, np.invert("bai" in file or "md5sum" in file))]

    # lists of files
    new_samples_df = []

    # iteratively copy the most processed files
    for file in files:
        # file info
        n_underscore = file.count("_")
        if n_underscore > 1:
            sample = "_".join([s for s in file.split("_")[:-1] if len(s) > 0]).split(".")[-1]
        else:
            sample = file.split("_")[0].split(".")[-1]
        patient = sample.split("_")[0]
        file_path = os.path.join(directory, file)
        sample_type = file.split(".")[0].split("_")[-1]

        # if the file is sorted then add it
        if not np.isin(patient, top_files_df['patient']):
            new_samples_df.append([sample, patient, file_path, sample_type])

    # create df
    new_samples_df = pd.DataFrame(new_samples_df, columns = ['sample', 'patient', 'file_path', 'sample_type'])
    top_files_df = pd.concat([top_files_df, new_samples_df])

    return top_files_df



def list_files_dir3(top_files_df, directory):
    # List all files in the directory
    files = os.listdir(directory)

    from collections import defaultdict

    def combine_files(files):
        combined_files = defaultdict(str)
        for file in files:
            key = "-".join(file.split("-")[:7]).split("_")[0]
            if combined_files[key]:
                combined_files[key] += ","
            combined_files[key] += file
        return combined_files.values()


    # lists of files
    new_samples_df = []

    new_samples = []

    sample_type_dict = {'FIXT':'tumor', 'PXD':'normal'}

    # iteratively copy the most processed files
    for file in combine_files(files):
        # file info
        patient = file.split("-")[2]
        sample = patient + "-" + file.split("_")[0].split("-")[-1]
        file_path = ",".join([os.path.join(directory, file) for file in file.split(",")])
        sample_type = sample_type_dict.get(file.split("-")[3])

        details = "-".join(file.split("-")[:2]) + "-" + "-".join(file.split("_")[0].split("-")[3:])

        if (sample + sample_type) not in new_samples:
            new_samples.append(sample + sample_type)
            new_samples_df.append([sample, patient, file_path, sample_type, details])

    # create df
    new_samples_df = pd.DataFrame(new_samples_df, columns = ['sample', 'patient', 'file_path', 'sample_type', 'details'])
    top_files_df = pd.concat([top_files_df, new_samples_df])

    # sort
    top_files_df = top_files_df.sort_values(['patient', 'sample_type'])

    return top_files_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # separate the directories
    dir1, dir2, dir3 = directories_csv.split(",")

    # process the directories
    top_files_df = list_files_dir1(dir1)
    top_files_df = list_files_dir2(top_files_df, dir2)
    top_files_df = list_files_dir3(top_files_df, dir3)

    # save the df
    top_files_df.to_csv(samples_outfile, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"

    directories_csv = "/.mounts/labs/reimandlab/private/generated_raw_data/Luhari_lung_2024-04-26/PERH_data-BAMS-ENC,/.mounts/labs/reimandlab/private/generated_raw_data/Luhari_lung_2024-04-26/PERH2-BAMS-ENC,/.mounts/labs/reimandlab/private/generated_raw_data/Luhari_lung_2024-04-26/decrypted-data-DNA-ENC"

    samples_outfile = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/data/estonia_2024/samples.tsv'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["directories_csv=", "samples_outfile="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--directories_csv"):
            directories_csv = str(arg)

        if opt in ("--samples_outfile"):
            samples_outfile = str(arg)
            
    main()




