# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

help_message = '''
Failed
'''

def process_df_to_sarek_csv(infile, outfile):
    df = pd.read_csv(infile)
    df.index = df['patient'].to_numpy()
    df = df.drop([861, 1284])

    # subset to fastq files
    df = df.loc[df['file_path'].str.contains("fastq"),:]

    # separate fastq file paths
    df['fastq_2'] = df['fastq_1'].str.split(",").str[1]
    df['fastq_1'] = df['fastq_1'].str.split(",").str[0]

    # set sample type
    df['status'] = df['sample'].map({'normal':0, 'tumor':1})

    # rename sample
    df['sample'] = df['patient'].astype('str') + "-" + df['sample']

    # repeated rows are 2
    df['lane'] = 'lane_1'
    duplicated_rows = df.duplicated(subset=['patient', 'sample'], keep='first')
    df.loc[duplicated_rows, 'lane'] = 'lane_2'

    # save to csv
    df.to_csv(outfile, index=False)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process samples file to sarek csv
    process_df_to_sarek_csv(samples_file, sarek_csv_outfile)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    samples_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/data/estonia_2024/samples.tsv'

    sarek_csv_outfile = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek/sarek_decrypted-data-DNA-ENC.csv'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["samples_file=", "sarek_csv_outfile="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--samples_file"):
            samples_file = str(arg)
                
        if opt in ("--sarek_csv_outfile"):
            sarek_csv_outfile = str(arg)

            
    main()




