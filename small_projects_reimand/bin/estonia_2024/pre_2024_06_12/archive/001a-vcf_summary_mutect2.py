# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def load_and_filter_pass(file):
    # load df
    df = pd.read_csv(file, sep='\t', comment="#", header=None)
    df = df.iloc[:,:-2]
    df.columns = "#CHROM	POS	ID	REF	ALT	QUAL	FILTER	INFO	FORMAT".split("\t")

    if not 'free' in file:
        # subset to pass
        df = df.loc[df['FILTER'] == 'PASS',:]

    # add sample details
    df['sample'] = file.split("/")[-1].split("-")[0]
    caller = file.split("/")[-2]
    caller = caller.split("-")[1].split("_")
    if len(caller) > 1:
        caller = "_".join(caller[:-1])
    else:
        caller = caller[0]

    df['variant_caller'] = caller

    print(file, file.split("/")[-3])

    return df



def load_and_process_dfs(file_list, threads):
    # multi-thread calculate p-values and fc
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(load_and_filter_pass, file_list))

    # collect converted dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    print(res_df['variant_caller'].unique())

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    file_regex = f"{results_dir}/variant_calling/mutect2/*/*filtered.vcf.gz"
    files = glob.glob(file_regex)

    print(files)

    # process and combine all dfs
    res_df = load_and_process_dfs(files, threads)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "r_script=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


