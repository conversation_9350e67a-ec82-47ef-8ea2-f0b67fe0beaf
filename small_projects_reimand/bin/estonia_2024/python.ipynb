{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "try:\n", "    sys.path.insert(1, \"/\".join(os.path.realpath(__file__).split(\"/\")[:-2]))\n", "except:\n", "    sys.path.insert(1, \"/\".join(os.getcwd().split(\"/\")[:-1]))\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d0f38108-eb42-40d1-8abe-ca0b44d5f40b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([40]),)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["vep_fields = ['Allele', 'Consequence', 'IMPACT', 'SYMB<PERSON>', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON', 'HGVSc', 'HGVSp', 'cDNA_position', 'CDS_position', 'Protein_position', 'Amino_acids', 'Codons', 'Existing_variation', 'DISTANCE', 'STRAND', 'FLAGS', 'VARIANT_CLASS', 'SYMBOL_SOURCE', 'HGNC_ID', 'CANONICAL', 'MANE_SELECT', 'MANE_PLUS_CLINICAL', 'TSL', 'APPRIS', 'CCDS', 'ENSP', 'SWISSPROT', 'TREMBL', 'UNIPARC', 'UNIPROT_ISOFORM', 'GENE_PHENO', 'SIFT', 'PolyPhen', 'DOMAINS', 'miRNA', 'AF', 'AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'gnomADe_AF', 'gnomADe_AFR_AF', 'gnomADe_AMR_AF', 'gnomADe_ASJ_AF', 'gnomADe_EAS_AF', 'gnomADe_FIN_AF', 'gnomADe_NFE_AF', 'gnomADe_OTH_AF', 'gnomADe_SAS_AF', 'gnomADg_AF', 'gnomADg_AFR_AF', 'gnomADg_AMI_AF', 'gnomADg_AMR_AF', 'gnomADg_ASJ_AF', 'gnomADg_EAS_AF', 'gnomADg_FIN_AF', 'gnomADg_MID_AF', 'gnomADg_NFE_AF', 'gnomADg_OTH_AF', 'gnomADg_SAS_AF', 'MAX_AF', 'MAX_AF_POPS', 'FREQS', 'CLIN_SIG', 'SOMATIC', 'PHENO', 'PUBMED', 'MOTIF_NAME', 'MOTIF_POS', 'HIGH_INF_POS', 'MOTIF_SCORE_CHANGE', 'TRANSCRIPTION_FACTORS']\n", "\n", "vf = np.array(vep_fields)\n", "np.where(vf == 'AF')\n"]}, {"cell_type": "code", "execution_count": null, "id": "68193c85-832b-471d-89b0-ef90b7c4b78a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "01f69717-6ace-4611-b941-2d291883b013", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "a13d2b15-67f3-41eb-aa2f-ba20ec7663e8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>SAMPLE</th>\n", "      <th>patient</th>\n", "      <th>variant_id</th>\n", "      <th>population_classification</th>\n", "      <th>pathogenic_classification</th>\n", "      <th>variant_type</th>\n", "      <th>mutation_type</th>\n", "      <th>mutation_details</th>\n", "      <th>cgc_gene</th>\n", "      <th>is_cgc_gene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>3411794</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1112:8:393:14:2,391:1,189:1,202:-99:PASS:3...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_3411794_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>PRDM16</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>6887657</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>germline</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=167,146|111,1...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:313,215:0.4:528:102,59:86,66:197,131:167,1...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_6887657_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>CAMTA1</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>11121270</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=3;CSQ=T|synonymous_variant|LOW|M...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1552:9:517:13:0,517:0,237:0,280:-99:PASS:3...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_11121270_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>MTOR</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>11130589</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=3;CSQ=A|synonymous_variant|LOW|M...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:3035:14:1010:19:0,1010:0,509:0,501:-99:PAS...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_11130589_G_A</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>MTOR</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>13778644</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>clustered_events;germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=195,182|165,1...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:377,342:0.489:719:83,58:103,115:239,227:19...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_13778644_T_A</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>PRDM2</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107198</th>\n", "      <td>chr9</td>\n", "      <td>136513000</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=3,12|8,28;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:15,36:0.691:51:6,14:9,13:15,35:3,12,8,28</td>\n", "      <td>********</td>\n", "      <td>chr9_136513000_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>NOTCH1</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107199</th>\n", "      <td>chrX</td>\n", "      <td>45107550</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=7,5|13,21;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:12,34:0.711:46:7,8:2,14:10,26:7,5,13,21</td>\n", "      <td>********</td>\n", "      <td>chrX_45107550_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>KDM6A</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107200</th>\n", "      <td>chrX</td>\n", "      <td>45107560</td>\n", "      <td>.</td>\n", "      <td>GT</td>\n", "      <td>G</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=7,5|12,22;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:12,34:0.73:46:6,8:2,13:9,26:7,5,12,22</td>\n", "      <td>********</td>\n", "      <td>chrX_45107560_GT_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>Indel</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>KDM6A</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107201</th>\n", "      <td>chrX</td>\n", "      <td>77682661</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>germline</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=39,33|41,31;D...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:72,72:0.505:144:21,28:30,24:53,54:39,33,41,31</td>\n", "      <td>********</td>\n", "      <td>chrX_77682661_G_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>ATRX</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107202</th>\n", "      <td>chrX</td>\n", "      <td>153549631</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>G</td>\n", "      <td>2723</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=4;CSQ=G|synonymous_variant|LOW|A...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:776:9:259:2:0,259:0,114:0,145:-99:PASS:370...</td>\n", "      <td>********</td>\n", "      <td>chrX_153549631_A_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>ATP2B3</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>107203 rows × 19 columns</p>\n", "</div>"], "text/plain": ["       CHROM        POS ID REF ALT  QUAL  \\\n", "0       chr1    3411794  .   T   C  3070   \n", "1       chr1    6887657  .   C   T     .   \n", "2       chr1   11121270  .   C   T  3070   \n", "3       chr1   11130589  .   G   A  3070   \n", "4       chr1   13778644  .   T   A     .   \n", "...      ...        ... ..  ..  ..   ...   \n", "107198  chr9  136513000  .   C   T     .   \n", "107199  chrX   45107550  .   T   C     .   \n", "107200  chrX   45107560  .  GT   G     .   \n", "107201  chrX   77682661  .   G   C     .   \n", "107202  chrX  153549631  .   A   G  2723   \n", "\n", "                                            FILTER  \\\n", "0                                             PASS   \n", "1                                         germline   \n", "2                                             PASS   \n", "3                                             PASS   \n", "4       clustered_events;germline;panel_of_normals   \n", "...                                            ...   \n", "107198                   germline;panel_of_normals   \n", "107199                   germline;panel_of_normals   \n", "107200                   germline;panel_of_normals   \n", "107201                                    germline   \n", "107202                                        PASS   \n", "\n", "                                                     INFO  \\\n", "0       MQ=60;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...   \n", "1       AS_FilterStatus=SITE;AS_SB_TABLE=167,146|111,1...   \n", "2       MQ=60;SNVHPOL=3;CSQ=T|synonymous_variant|LOW|M...   \n", "3       MQ=60;SNVHPOL=3;CSQ=A|synonymous_variant|LOW|M...   \n", "4       AS_FilterStatus=SITE;AS_SB_TABLE=195,182|165,1...   \n", "...                                                   ...   \n", "107198  AS_FilterStatus=SITE;AS_SB_TABLE=3,12|8,28;DP=...   \n", "107199  AS_FilterStatus=SITE;AS_SB_TABLE=7,5|13,21;DP=...   \n", "107200  AS_FilterStatus=SITE;AS_SB_TABLE=7,5|12,22;DP=...   \n", "107201  AS_FilterStatus=SITE;AS_SB_TABLE=39,33|41,31;D...   \n", "107202  MQ=60;SNVHPOL=4;CSQ=G|synonymous_variant|LOW|A...   \n", "\n", "                                      FORMAT  \\\n", "0       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "1               GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "2       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "3       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "4               GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "...                                      ...   \n", "107198          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107199          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107200          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107201          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107202  GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "\n", "                                                   SAMPLE     patient  \\\n", "0       1/1:1112:8:393:14:2,391:1,189:1,202:-99:PASS:3...  P201103547   \n", "1       0/1:313,215:0.4:528:102,59:86,66:197,131:167,1...  P201103547   \n", "2       1/1:1552:9:517:13:0,517:0,237:0,280:-99:PASS:3...  P201103547   \n", "3       1/1:3035:14:1010:19:0,1010:0,509:0,501:-99:PAS...  P201103547   \n", "4       0/1:377,342:0.489:719:83,58:103,115:239,227:19...  P201103547   \n", "...                                                   ...         ...   \n", "107198       0/1:15,36:0.691:51:6,14:9,13:15,35:3,12,8,28    ********   \n", "107199        0/1:12,34:0.711:46:7,8:2,14:10,26:7,5,13,21    ********   \n", "107200          0/1:12,34:0.73:46:6,8:2,13:9,26:7,5,12,22    ********   \n", "107201  0/1:72,72:0.505:144:21,28:30,24:53,54:39,33,41,31    ********   \n", "107202  1/1:776:9:259:2:0,259:0,114:0,145:-99:PASS:370...    ********   \n", "\n", "                variant_id population_classification  \\\n", "0         chr1_3411794_T_C                Population   \n", "1         chr1_6887657_C_T                Population   \n", "2        chr1_11121270_C_T                Population   \n", "3        chr1_11130589_G_A                Population   \n", "4        chr1_13778644_T_A                Population   \n", "...                    ...                       ...   \n", "107198  chr9_136513000_C_T                Population   \n", "107199   chrX_45107550_T_C                Population   \n", "107200  chrX_45107560_GT_G                Population   \n", "107201   chrX_77682661_G_C                Population   \n", "107202  chrX_153549631_A_G                Population   \n", "\n", "       pathogenic_classification variant_type      mutation_type  \\\n", "0                  Likely benign          SNV      coding_region   \n", "1                  Likely benign          SNV  non-coding_region   \n", "2                  Likely benign          SNV      coding_region   \n", "3                  Likely benign          SNV      coding_region   \n", "4                  Likely benign          SNV      coding_region   \n", "...                          ...          ...                ...   \n", "107198             Likely benign          SNV  non-coding_region   \n", "107199             Likely benign          SNV  non-coding_region   \n", "107200             Likely benign        Indel  non-coding_region   \n", "107201             Likely benign          SNV      coding_region   \n", "107202             Likely benign          SNV      coding_region   \n", "\n", "       mutation_details cgc_gene  is_cgc_gene  \n", "0              missense   PRDM16         True  \n", "1            synonymous   CAMTA1         True  \n", "2            synonymous     MTOR         True  \n", "3            synonymous     MTOR         True  \n", "4              missense    PRDM2         True  \n", "...                 ...      ...          ...  \n", "107198       synonymous   NOTCH1         True  \n", "107199       synonymous    KDM6A         True  \n", "107200       synonymous    KDM6A         True  \n", "107201         missense     ATRX         True  \n", "107202       synonymous   ATP2B3         True  \n", "\n", "[107203 rows x 19 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/002-cgc_only_protein_impact.tsv'\n", "\n", "df2 = pd.read_csv(file, sep='\\t')\n", "\n", "df2"]}, {"cell_type": "code", "execution_count": 24, "id": "5a919bdc-a9fe-4fe3-92a3-13f8e9cb5849", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Q1 (25th percentile): 31.54\n", "Q2 (median, 50th percentile): 41.72\n", "Q3 (75th percentile): 91.56\n"]}], "source": ["q1 = df2[5].quantile(0.25)\n", "q2 = df2[5].quantile(0.5)  # Median (Q2)\n", "q3 = df2[5].quantile(0.75)\n", "\n", "print(\"Q1 (25th percentile):\", q1)\n", "print(\"Q2 (median, 50th percentile):\", q2)\n", "print(\"Q3 (75th percentile):\", q3)"]}, {"cell_type": "code", "execution_count": 12, "id": "165f6bcf-4e99-4454-a5fc-dba0548a44f5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>942451</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>1455.03</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=76;CIGAR=1X;DP=76...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:76:0,76:0:0:76:1723:-155.29,-22.8783,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>952421</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>G</td>\n", "      <td>2031.20</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=88;CIGAR=1X;DP=88...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:88:0,88:0:0:88:2335:-210.395,-26.4906,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>961945</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>C</td>\n", "      <td>2414.62</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=105;CIGAR=1X;DP=1...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:107:0,105:0:0:105:2726:-245.356,-31.6082,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>962184</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>101.18</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=5;CIGAR=1X;DP=5;D...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:5:0,5:0:0:5:133:-12.235,-1.50515,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>965125</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>C</td>\n", "      <td>3411.31</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=146;CIGAR=1X;DP=1...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:146:0,146:0:0:146:3829:-344.845,-43.9504,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>chrX</td>\n", "      <td>153950388</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>1369.20</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=69;CIGAR=1X;DP=69...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:69:0,69:0:0:69:1556:-140.26,-20.7711,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>chrX</td>\n", "      <td>154444391</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>CT</td>\n", "      <td>128.79</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=8;CIGAR=1M1I8M;DP...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:8:0,8:0:0:8:182:-16.6069,-2.40824,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3456</th>\n", "      <td>chrX</td>\n", "      <td>154467565</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>G</td>\n", "      <td>1545.20</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=77;CIGAR=1X;DP=77...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:77:0,77:0:0:77:1758:-158.416,-23.1793,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3457</th>\n", "      <td>chrX</td>\n", "      <td>154467904</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>G</td>\n", "      <td>1287.15</td>\n", "      <td>.</td>\n", "      <td>AB=0;ABP=0;AC=2;AF=1;AN=2;AO=66;CIGAR=1X;DP=66...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>1/1:66:0,66:0:0:66:1463:-131.887,-19.868,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3458</th>\n", "      <td>chrY</td>\n", "      <td>9467257</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>1574.13</td>\n", "      <td>.</td>\n", "      <td>AB=0.494624;ABP=3.08035;AC=1;AF=0.5;AN=2;AO=13...</td>\n", "      <td>GT:DP:AD:RO:QR:AO:QA:GL</td>\n", "      <td>0/1:279:141,138:141:3157:138:3219:-187.66,0,-1...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3459 rows × 10 columns</p>\n", "</div>"], "text/plain": ["         0          1  2  3   4        5  6  \\\n", "0     chr1     942451  .  T   C  1455.03  .   \n", "1     chr1     952421  .  A   G  2031.20  .   \n", "2     chr1     961945  .  G   C  2414.62  .   \n", "3     chr1     962184  .  T   C   101.18  .   \n", "4     chr1     965125  .  G   C  3411.31  .   \n", "...    ...        ... .. ..  ..      ... ..   \n", "3454  chrX  153950388  .  G   A  1369.20  .   \n", "3455  chrX  154444391  .  C  CT   128.79  .   \n", "3456  chrX  154467565  .  T   G  1545.20  .   \n", "3457  chrX  154467904  .  C   G  1287.15  .   \n", "3458  chrY    9467257  .  G   A  1574.13  .   \n", "\n", "                                                      7  \\\n", "0     AB=0;ABP=0;AC=2;AF=1;AN=2;AO=76;CIGAR=1X;DP=76...   \n", "1     AB=0;ABP=0;AC=2;AF=1;AN=2;AO=88;CIGAR=1X;DP=88...   \n", "2     AB=0;ABP=0;AC=2;AF=1;AN=2;AO=105;CIGAR=1X;DP=1...   \n", "3     AB=0;ABP=0;AC=2;AF=1;AN=2;AO=5;CIGAR=1X;DP=5;D...   \n", "4     AB=0;ABP=0;AC=2;AF=1;AN=2;AO=146;CIGAR=1X;DP=1...   \n", "...                                                 ...   \n", "3454  AB=0;ABP=0;AC=2;AF=1;AN=2;AO=69;CIGAR=1X;DP=69...   \n", "3455  AB=0;ABP=0;AC=2;AF=1;AN=2;AO=8;CIGAR=1M1I8M;DP...   \n", "3456  AB=0;ABP=0;AC=2;AF=1;AN=2;AO=77;CIGAR=1X;DP=77...   \n", "3457  AB=0;ABP=0;AC=2;AF=1;AN=2;AO=66;CIGAR=1X;DP=66...   \n", "3458  AB=0.494624;ABP=3.08035;AC=1;AF=0.5;AN=2;AO=13...   \n", "\n", "                            8  \\\n", "0     GT:DP:AD:RO:QR:AO:QA:GL   \n", "1     GT:DP:AD:RO:QR:AO:QA:GL   \n", "2     GT:DP:AD:RO:QR:AO:QA:GL   \n", "3     GT:DP:AD:RO:QR:AO:QA:GL   \n", "4     GT:DP:AD:RO:QR:AO:QA:GL   \n", "...                       ...   \n", "3454  GT:DP:AD:RO:QR:AO:QA:GL   \n", "3455  GT:DP:AD:RO:QR:AO:QA:GL   \n", "3456  GT:DP:AD:RO:QR:AO:QA:GL   \n", "3457  GT:DP:AD:RO:QR:AO:QA:GL   \n", "3458  GT:DP:AD:RO:QR:AO:QA:GL   \n", "\n", "                                                      9  \n", "0            1/1:76:0,76:0:0:76:1723:-155.29,-22.8783,0  \n", "1           1/1:88:0,88:0:0:88:2335:-210.395,-26.4906,0  \n", "2        1/1:107:0,105:0:0:105:2726:-245.356,-31.6082,0  \n", "3                1/1:5:0,5:0:0:5:133:-12.235,-1.50515,0  \n", "4        1/1:146:0,146:0:0:146:3829:-344.845,-43.9504,0  \n", "...                                                 ...  \n", "3454         1/1:69:0,69:0:0:69:1556:-140.26,-20.7711,0  \n", "3455            1/1:8:0,8:0:0:8:182:-16.6069,-2.40824,0  \n", "3456        1/1:77:0,77:0:0:77:1758:-158.416,-23.1793,0  \n", "3457         1/1:66:0,66:0:0:66:1463:-131.887,-19.868,0  \n", "3458  0/1:279:141,138:141:3157:138:3219:-187.66,0,-1...  \n", "\n", "[3459 rows x 10 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_vcfs/consensus_vcfs/********-tumor/0002.vcf.gz'\n", "# file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_vcfs/strelka_raw/ZZZ04911-tumor.strelka.variants.vcf.gz'\n", "\n", "df3 = pd.read_csv(file, sep='\\t', comment='#', header=None, nrows=100000, compression='gzip')\n", "\n", "df3"]}, {"cell_type": "code", "execution_count": 13, "id": "ba8784e1-5126-46be-98a0-a8ed1470a819", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['.'], dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df3[6].unique()"]}, {"cell_type": "code", "execution_count": 17, "id": "6329f51f-cc3e-4f2a-a275-be80fbfc49d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['PASS',\n", "       'clustered_events;contamination;map_qual;slippage;weak_evidence',\n", "       'clustered_events;germline;haplotype;map_qual;panel_of_normals',\n", "       'clustered_events;germline;haplotype;map_qual', 'weak_evidence',\n", "       'germline', 'contamination;weak_evidence', 'germline;haplotype',\n", "       'haplotype', 'clustered_events;germline;multiallelic',\n", "       'clustered_events;germline;haplotype',\n", "       'clustered_events;haplotype', 'clustered_events',\n", "       'clustered_events;germline', 'clustered_events;haplotype;map_qual',\n", "       'clustered_events;haplotype;weak_evidence',\n", "       'clustered_events;multiallelic;weak_evidence',\n", "       'map_qual;strand_bias', 'map_qual',\n", "       'clustered_events;panel_of_normals',\n", "       'base_qual;haplotype;weak_evidence', 'haplotype;weak_evidence',\n", "       'contamination;germline;haplotype',\n", "       'clustered_events;weak_evidence', 'clustered_events;multiallelic',\n", "       'clustered_events;germline;map_qual;panel_of_normals',\n", "       'clustered_events;haplotype;position',\n", "       'contamination;germline;haplotype;map_qual;slippage;weak_evidence',\n", "       'contamination;germline;haplotype;map_qual', 'germline;map_qual',\n", "       'base_qual;clustered_events;haplotype',\n", "       'clustered_events;position',\n", "       'clustered_events;germline;weak_evidence',\n", "       'germline;weak_evidence',\n", "       'clustered_events;fragment;germline;haplotype;map_qual',\n", "       'clustered_events;fragment;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;fragment;germline;haplotype',\n", "       'clustered_events;fragment;germline;haplotype;weak_evidence',\n", "       'clustered_events;fragment;haplotype',\n", "       'clustered_events;germline;haplotype;weak_evidence',\n", "       'haplotype;map_qual', 'fragment;weak_evidence',\n", "       'fragment;germline',\n", "       'clustered_events;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;fragment',\n", "       'clustered_events;fragment;haplotype;weak_evidence',\n", "       'contamination;germline',\n", "       'contamination;fragment;germline;slippage;weak_evidence',\n", "       'germline;haplotype;map_qual;panel_of_normals',\n", "       'germline;haplotype;map_qual',\n", "       'clustered_events;contamination;germline;haplotype;map_qual',\n", "       'contamination;germline;panel_of_normals;weak_evidence',\n", "       'clustered_events;contamination;germline;haplotype',\n", "       'clustered_events;germline;slippage',\n", "       'clustered_events;germline;haplotype;panel_of_normals',\n", "       'germline;panel_of_normals',\n", "       'base_qual;clustered_events;germline;haplotype',\n", "       'base_qual;clustered_events;fragment;germline;haplotype',\n", "       'clustered_events;fragment;germline',\n", "       'germline;haplotype;panel_of_normals',\n", "       'clustered_events;germline;panel_of_normals',\n", "       'germline;multiallelic', 'multiallelic;panel_of_normals',\n", "       'panel_of_normals', 'clustered_events;haplotype;panel_of_normals',\n", "       'clustered_events;contamination;germline;panel_of_normals',\n", "       'clustered_events;contamination;germline;haplotype;panel_of_normals',\n", "       'contamination;germline;weak_evidence',\n", "       'clustered_events;contamination;haplotype',\n", "       'contamination;germline;panel_of_normals',\n", "       'germline;haplotype;panel_of_normals;slippage',\n", "       'base_qual;clustered_events;haplotype;weak_evidence',\n", "       'clustered_events;map_qual', 'fragment',\n", "       'clustered_events;contamination;germline;haplotype;weak_evidence',\n", "       'germline;multiallelic;panel_of_normals',\n", "       'base_qual;clustered_events;haplotype;strand_bias',\n", "       'base_qual;clustered_events;haplotype;strand_bias;weak_evidence',\n", "       'clustered_events;fragment;germline;haplotype;panel_of_normals',\n", "       'clustered_events;contamination;germline;panel_of_normals;weak_evidence',\n", "       'base_qual;haplotype', 'base_qual;clustered_events;weak_evidence',\n", "       'clustered_events;map_qual;multiallelic',\n", "       'fragment;haplotype;weak_evidence', 'fragment;haplotype',\n", "       'germline;slippage',\n", "       'clustered_events;contamination;germline;haplotype;panel_of_normals;weak_evidence',\n", "       'slippage;weak_evidence', 'slippage',\n", "       'base_qual;clustered_events;haplotype;panel_of_normals;strand_bias',\n", "       'clustered_events;haplotype;panel_of_normals;strand_bias',\n", "       'clustered_events;haplotype;strand_bias',\n", "       'clustered_events;haplotype;position;weak_evidence',\n", "       'clustered_events;contamination;haplotype;weak_evidence',\n", "       'multiallelic',\n", "       'clustered_events;contamination;germline;weak_evidence',\n", "       'germline;slippage;weak_evidence', 'germline;haplotype;slippage',\n", "       'haplotype;position', 'contamination;slippage;weak_evidence',\n", "       'base_qual;clustered_events;contamination;multiallelic;slippage;weak_evidence',\n", "       'contamination;germline;haplotype;panel_of_normals',\n", "       'contamination;germline;haplotype;weak_evidence',\n", "       'clustered_events;germline;haplotype;slippage',\n", "       'clustered_events;contamination;germline;panel_of_normals;slippage',\n", "       'clustered_events;multiallelic;panel_of_normals',\n", "       'base_qual;germline;slippage;weak_evidence',\n", "       'base_qual;weak_evidence',\n", "       'contamination;germline;haplotype;slippage;weak_evidence',\n", "       'base_qual;germline',\n", "       'base_qual;clustered_events;fragment;haplotype',\n", "       'clustered_events;contamination;fragment;germline;haplotype',\n", "       'clustered_events;contamination;panel_of_normals;weak_evidence',\n", "       'clustered_events;contamination;weak_evidence',\n", "       'clustered_events;fragment;haplotype;map_qual',\n", "       'fragment;germline;haplotype;weak_evidence',\n", "       'base_qual;clustered_events;haplotype;map_qual;weak_evidence',\n", "       'base_qual', 'germline;multiallelic;slippage',\n", "       'contamination;germline;slippage',\n", "       'clustered_events;slippage;weak_evidence',\n", "       'contamination;germline;slippage;weak_evidence',\n", "       'contamination;germline;haplotype;slippage',\n", "       'contamination;haplotype;slippage;weak_evidence',\n", "       'map_qual;multiallelic', 'map_qual;weak_evidence',\n", "       'contamination;map_qual;multiallelic',\n", "       'clustered_events;contamination',\n", "       'clustered_events;map_qual;panel_of_normals;slippage',\n", "       'germline;map_qual;panel_of_normals',\n", "       'clustered_events;germline;panel_of_normals;weak_evidence',\n", "       'clustered_events;slippage',\n", "       'clustered_events;contamination;haplotype;panel_of_normals',\n", "       'contamination;haplotype;weak_evidence', 'strand_bias',\n", "       'contamination;haplotype', 'germline;haplotype;weak_evidence',\n", "       'clustered_events;germline;slippage;weak_evidence',\n", "       'contamination;germline;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;germline;map_qual',\n", "       'clustered_events;map_qual;weak_evidence',\n", "       'clustered_events;contamination;orientation;weak_evidence',\n", "       'clustered_events;contamination;haplotype;orientation;weak_evidence',\n", "       'base_qual;germline;haplotype;panel_of_normals',\n", "       'germline;panel_of_normals;slippage',\n", "       'clustered_events;contamination;multiallelic;weak_evidence',\n", "       'clustered_events;germline;multiallelic;panel_of_normals',\n", "       'clustered_events;germline;haplotype;slippage;weak_evidence',\n", "       'clustered_events;contamination;germline;multiallelic',\n", "       'clustered_events;contamination;germline;slippage',\n", "       'clustered_events;germline;map_qual;slippage;weak_evidence',\n", "       'germline;multiallelic;panel_of_normals;slippage',\n", "       'fragment;germline;weak_evidence',\n", "       'clustered_events;contamination;haplotype;map_qual',\n", "       'clustered_events;contamination;germline;haplotype;slippage;weak_evidence',\n", "       'base_qual;contamination;germline;haplotype;weak_evidence',\n", "       'base_qual;clustered_events;germline;slippage',\n", "       'clustered_events;contamination;germline',\n", "       'multiallelic;weak_evidence',\n", "       'base_qual;clustered_events;germline;slippage;weak_evidence',\n", "       'clustered_events;germline;haplotype;panel_of_normals;slippage',\n", "       'base_qual;clustered_events;haplotype;map_qual',\n", "       'base_qual;fragment;haplotype',\n", "       'fragment;germline;slippage;weak_evidence',\n", "       'base_qual;strand_bias',\n", "       'clustered_events;fragment;germline;multiallelic;panel_of_normals',\n", "       'clustered_events;contamination;fragment;germline;weak_evidence',\n", "       'orientation;weak_evidence', 'multiallelic;slippage',\n", "       'clustered_events;fragment;weak_evidence',\n", "       'base_qual;clustered_events',\n", "       'clustered_events;multiallelic;slippage',\n", "       'clustered_events;contamination;germline;slippage;weak_evidence',\n", "       'base_qual;clustered_events;contamination;germline;haplotype;weak_evidence',\n", "       'fragment;germline;map_qual', 'fragment;germline;haplotype',\n", "       'base_qual;fragment',\n", "       'fragment;germline;haplotype;panel_of_normals',\n", "       'germline;multiallelic;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;fragment;multiallelic',\n", "       'clustered_events;haplotype;strand_bias;weak_evidence',\n", "       'base_qual;clustered_events;contamination;haplotype;strand_bias;weak_evidence',\n", "       'contamination;fragment;germline;panel_of_normals',\n", "       'clustered_events;fragment;map_qual',\n", "       'fragment;germline;panel_of_normals',\n", "       'base_qual;clustered_events;contamination;weak_evidence',\n", "       'clustered_events;multiallelic;panel_of_normals;slippage',\n", "       'multiallelic;panel_of_normals;slippage',\n", "       'germline;panel_of_normals;slippage;weak_evidence',\n", "       'multiallelic;slippage;weak_evidence',\n", "       'clustered_events;germline;haplotype;panel_of_normals;weak_evidence',\n", "       'germline;haplotype;slippage;weak_evidence',\n", "       'germline;panel_of_normals;weak_evidence',\n", "       'clustered_events;germline;multiallelic;weak_evidence',\n", "       'panel_of_normals;slippage',\n", "       'haplotype;map_qual;slippage;weak_evidence',\n", "       'base_qual;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;germline;multiallelic;panel_of_normals;slippage',\n", "       'contamination;germline;haplotype;panel_of_normals;weak_evidence',\n", "       'clustered_events;fragment;germline;panel_of_normals',\n", "       'base_qual;germline;slippage',\n", "       'contamination;fragment;germline;haplotype;panel_of_normals;slippage',\n", "       'panel_of_normals;weak_evidence',\n", "       'base_qual;germline;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;haplotype;panel_of_normals;weak_evidence',\n", "       'contamination;fragment;germline',\n", "       'contamination;fragment;germline;weak_evidence',\n", "       'base_qual;contamination;germline;haplotype;panel_of_normals',\n", "       'contamination;fragment;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;slippage;weak_evidence',\n", "       'contamination;slippage',\n", "       'base_qual;clustered_events;contamination;germline',\n", "       'clustered_events;contamination;slippage;weak_evidence',\n", "       'fragment;germline;multiallelic;panel_of_normals;slippage;weak_evidence',\n", "       'base_qual;clustered_events;haplotype;panel_of_normals;weak_evidence',\n", "       'position', 'map_qual;slippage;weak_evidence',\n", "       'base_qual;clustered_events;germline;haplotype;slippage',\n", "       'fragment;germline;map_qual;weak_evidence',\n", "       'clustered_events;germline;panel_of_normals;slippage;weak_evidence',\n", "       'base_qual;contamination;germline;haplotype;slippage;weak_evidence',\n", "       'clustered_events;contamination;haplotype;slippage;weak_evidence',\n", "       'fragment;haplotype;map_qual',\n", "       'clustered_events;orientation;weak_evidence',\n", "       'germline;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;germline;multiallelic;slippage',\n", "       'base_qual;germline;haplotype', 'base_qual;slippage',\n", "       'clustered_events;germline;multiallelic;slippage;weak_evidence',\n", "       'base_qual;fragment;haplotype;weak_evidence',\n", "       'haplotype;map_qual;weak_evidence', 'base_qual;fragment;germline',\n", "       'base_qual;clustered_events;germline;weak_evidence',\n", "       'base_qual;map_qual;weak_evidence',\n", "       'contamination;fragment;germline;panel_of_normals;weak_evidence',\n", "       'clustered_events;panel_of_normals;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;haplotype;panel_of_normals',\n", "       'clustered_events;contamination;germline;haplotype;panel_of_normals;slippage;weak_evidence',\n", "       'base_qual;contamination;germline',\n", "       'clustered_events;fragment;germline;panel_of_normals;weak_evidence',\n", "       'contamination;fragment;germline;haplotype;slippage;weak_evidence',\n", "       'contamination;fragment;germline;haplotype',\n", "       'panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;germline;panel_of_normals;slippage',\n", "       'clustered_events;haplotype;map_qual;panel_of_normals',\n", "       'base_qual;clustered_events;fragment;haplotype;weak_evidence',\n", "       'base_qual;contamination;fragment;germline;haplotype',\n", "       'clustered_events;contamination;germline;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;contamination;haplotype;orientation',\n", "       'clustered_events;haplotype;slippage;weak_evidence',\n", "       'haplotype;slippage;weak_evidence',\n", "       'clustered_events;fragment;germline;multiallelic;weak_evidence',\n", "       'contamination;fragment;germline;multiallelic;slippage;weak_evidence',\n", "       'clustered_events;germline;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;fragment;haplotype;panel_of_normals',\n", "       'clustered_events;contamination;fragment;germline;haplotype;weak_evidence',\n", "       'base_qual;contamination;germline;weak_evidence',\n", "       'base_qual;clustered_events;haplotype;panel_of_normals',\n", "       'contamination;fragment;germline;slippage',\n", "       'haplotype;panel_of_normals', 'fragment;map_qual',\n", "       'clustered_events;germline;panel_of_normals;position',\n", "       'base_qual;clustered_events;germline',\n", "       'germline;multiallelic;slippage;weak_evidence',\n", "       'fragment;germline;slippage',\n", "       'clustered_events;contamination;fragment;germline;haplotype;slippage;weak_evidence',\n", "       'orientation', 'clustered_events;orientation',\n", "       'clustered_events;fragment;map_qual;weak_evidence',\n", "       'clustered_events;haplotype;orientation;weak_evidence',\n", "       'clustered_events;contamination;germline;haplotype;map_qual;slippage;weak_evidence',\n", "       'contamination;panel_of_normals;weak_evidence',\n", "       'base_qual;clustered_events;contamination;germline;haplotype',\n", "       'contamination;haplotype;slippage',\n", "       'contamination;multiallelic;slippage;weak_evidence',\n", "       'base_qual;germline;haplotype;weak_evidence',\n", "       'base_qual;fragment;weak_evidence',\n", "       'clustered_events;fragment;multiallelic;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline',\n", "       'base_qual;germline;haplotype;slippage;weak_evidence',\n", "       'base_qual;haplotype;strand_bias',\n", "       'clustered_events;contamination;panel_of_normals',\n", "       'clustered_events;haplotype;panel_of_normals;weak_evidence',\n", "       'base_qual;clustered_events;contamination;germline;weak_evidence',\n", "       'clustered_events;panel_of_normals;slippage',\n", "       'base_qual;fragment;map_qual;weak_evidence',\n", "       'fragment;haplotype;map_qual;weak_evidence',\n", "       'base_qual;clustered_events;germline;panel_of_normals',\n", "       'haplotype;orientation;weak_evidence',\n", "       'contamination;haplotype;panel_of_normals;weak_evidence',\n", "       'contamination;germline;multiallelic;slippage;weak_evidence',\n", "       'base_qual;multiallelic',\n", "       'contamination;fragment;germline;panel_of_normals;slippage;weak_evidence',\n", "       'contamination;germline;map_qual;weak_evidence',\n", "       'fragment;slippage;weak_evidence',\n", "       'base_qual;clustered_events;germline;haplotype;weak_evidence',\n", "       'germline;multiallelic;panel_of_normals;slippage;weak_evidence',\n", "       'base_qual;slippage;weak_evidence',\n", "       'clustered_events;map_qual;panel_of_normals',\n", "       'map_qual;panel_of_normals;slippage',\n", "       'clustered_events;map_qual;strand_bias',\n", "       'fragment;panel_of_normals', 'map_qual;panel_of_normals',\n", "       'clustered_events;map_qual;panel_of_normals;weak_evidence',\n", "       'base_qual;haplotype;slippage;weak_evidence',\n", "       'clustered_events;contamination;panel_of_normals;slippage',\n", "       'base_qual;clustered_events;germline;multiallelic;weak_evidence',\n", "       'base_qual;clustered_events;map_qual',\n", "       'base_qual;germline;multiallelic;slippage;weak_evidence',\n", "       'clustered_events;fragment;germline;weak_evidence',\n", "       'contamination',\n", "       'base_qual;clustered_events;map_qual;weak_evidence',\n", "       'contamination;germline;haplotype;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;position;weak_evidence', 'haplotype;slippage',\n", "       'clustered_events;fragment;germline;map_qual',\n", "       'clustered_events;multiallelic;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;haplotype;slippage',\n", "       'clustered_events;contamination;germline;haplotype;slippage',\n", "       'base_qual;clustered_events;contamination;haplotype;weak_evidence',\n", "       'clustered_events;contamination;germline;multiallelic;slippage;weak_evidence',\n", "       'clustered_events;fragment;germline;multiallelic;slippage',\n", "       'base_qual;clustered_events;germline;panel_of_normals;slippage',\n", "       'haplotype;panel_of_normals;slippage',\n", "       'base_qual;strand_bias;weak_evidence',\n", "       'base_qual;clustered_events;strand_bias',\n", "       'clustered_events;contamination;fragment;germline;haplotype;map_qual;panel_of_normals',\n", "       'clustered_events;fragment;germline;slippage;weak_evidence',\n", "       'base_qual;contamination;germline;slippage;weak_evidence',\n", "       'contamination;germline;panel_of_normals;slippage',\n", "       'base_qual;clustered_events;contamination;fragment;germline;slippage',\n", "       'clustered_events;fragment;haplotype;position;weak_evidence',\n", "       'haplotype;position;weak_evidence',\n", "       'clustered_events;haplotype;orientation',\n", "       'clustered_events;contamination;haplotype;position;weak_evidence',\n", "       'clustered_events;contamination;haplotype;map_qual;orientation;weak_evidence',\n", "       'clustered_events;contamination;haplotype;map_qual;weak_evidence',\n", "       'clustered_events;germline;map_qual;panel_of_normals;slippage',\n", "       'clustered_events;panel_of_normals;strand_bias',\n", "       'clustered_events;strand_bias',\n", "       'clustered_events;germline;panel_of_normals;strand_bias',\n", "       'haplotype;panel_of_normals;strand_bias', 'haplotype;strand_bias',\n", "       'clustered_events;germline;map_qual;weak_evidence',\n", "       'clustered_events;contamination;germline;haplotype;map_qual;panel_of_normals',\n", "       'map_qual;orientation;strand_bias',\n", "       'clustered_events;contamination;germline;map_qual;panel_of_normals',\n", "       'base_qual;clustered_events;contamination;germline;panel_of_normals',\n", "       'contamination;fragment;germline;haplotype;panel_of_normals',\n", "       'base_qual;germline;map_qual;weak_evidence',\n", "       'clustered_events;haplotype;map_qual;panel_of_normals;strand_bias',\n", "       'clustered_events;contamination;germline;map_qual;weak_evidence',\n", "       'fragment;germline;haplotype;map_qual',\n", "       'contamination;germline;multiallelic;weak_evidence',\n", "       'clustered_events;contamination;fragment;haplotype;weak_evidence',\n", "       'clustered_events;germline;map_qual;slippage',\n", "       'base_qual;clustered_events;contamination;germline;slippage;weak_evidence',\n", "       'clustered_events;contamination;fragment;weak_evidence',\n", "       'base_qual;contamination;germline;slippage',\n", "       'clustered_events;fragment;germline;multiallelic',\n", "       'base_qual;clustered_events;contamination;fragment;germline;slippage;weak_evidence',\n", "       'base_qual;germline;multiallelic;panel_of_normals;slippage',\n", "       'contamination;germline;haplotype;map_qual;panel_of_normals',\n", "       'contamination;germline;haplotype;panel_of_normals;slippage',\n", "       'multiallelic;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;germline;position',\n", "       'clustered_events;contamination;germline;haplotype;position;weak_evidence',\n", "       'base_qual;multiallelic;weak_evidence',\n", "       'clustered_events;contamination;germline;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;germline;map_qual;multiallelic',\n", "       'base_qual;clustered_events;fragment;haplotype;map_qual',\n", "       'contamination;fragment;germline;haplotype;panel_of_normals;weak_evidence',\n", "       'base_qual;clustered_events;fragment;haplotype;map_qual;weak_evidence',\n", "       'contamination;germline;map_qual',\n", "       'base_qual;contamination;fragment;germline',\n", "       'base_qual;clustered_events;fragment;haplotype;panel_of_normals;weak_evidence',\n", "       'base_qual;clustered_events;fragment;germline;weak_evidence',\n", "       'haplotype;panel_of_normals;weak_evidence',\n", "       'contamination;fragment;germline;haplotype;map_qual',\n", "       'base_qual;fragment;haplotype;map_qual',\n", "       'base_qual;clustered_events;fragment',\n", "       'base_qual;clustered_events;contamination;haplotype',\n", "       'germline;map_qual;weak_evidence',\n", "       'clustered_events;fragment;panel_of_normals',\n", "       'fragment;germline;panel_of_normals;slippage',\n", "       'base_qual;haplotype;map_qual', 'germline;haplotype;position',\n", "       'clustered_events;contamination;germline;multiallelic;weak_evidence',\n", "       'base_qual;clustered_events;fragment;weak_evidence',\n", "       'base_qual;contamination;germline;panel_of_normals;slippage',\n", "       'base_qual;map_qual;strand_bias',\n", "       'base_qual;clustered_events;germline;multiallelic;panel_of_normals;slippage',\n", "       'contamination;map_qual;weak_evidence',\n", "       'germline;multiallelic;panel_of_normals;weak_evidence',\n", "       'fragment;germline;multiallelic;slippage;weak_evidence',\n", "       'fragment;germline;multiallelic;panel_of_normals',\n", "       'clustered_events;fragment;germline;haplotype;slippage;weak_evidence',\n", "       'clustered_events;haplotype;map_qual;slippage;weak_evidence',\n", "       'base_qual;germline;multiallelic;panel_of_normals',\n", "       'fragment;multiallelic;weak_evidence',\n", "       'base_qual;clustered_events;fragment;map_qual;weak_evidence',\n", "       'fragment;slippage', 'base_qual;germline;panel_of_normals',\n", "       'clustered_events;fragment;haplotype;slippage;weak_evidence',\n", "       'base_qual;clustered_events;contamination;germline;haplotype;slippage;weak_evidence',\n", "       'base_qual;contamination;germline;haplotype',\n", "       'clustered_events;haplotype;map_qual;position',\n", "       'base_qual;panel_of_normals;weak_evidence',\n", "       'base_qual;contamination;haplotype;weak_evidence',\n", "       'clustered_events;contamination;panel_of_normals;slippage;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;map_qual;weak_evidence',\n", "       'position;weak_evidence',\n", "       'clustered_events;fragment;map_qual;multiallelic',\n", "       'base_qual;clustered_events;contamination;germline;slippage',\n", "       'clustered_events;germline;multiallelic;panel_of_normals;weak_evidence',\n", "       'base_qual;contamination;germline;panel_of_normals;weak_evidence',\n", "       'haplotype;strand_bias;weak_evidence',\n", "       'base_qual;germline;multiallelic;weak_evidence',\n", "       'fragment;multiallelic',\n", "       'contamination;germline;map_qual;panel_of_normals;weak_evidence',\n", "       'base_qual;germline;panel_of_normals;weak_evidence',\n", "       'contamination;fragment;germline;haplotype;map_qual;panel_of_normals',\n", "       'base_qual;clustered_events;contamination;germline;haplotype;panel_of_normals',\n", "       'base_qual;clustered_events;contamination;fragment;germline;haplotype',\n", "       'base_qual;germline;multiallelic',\n", "       'base_qual;clustered_events;panel_of_normals;weak_evidence',\n", "       'base_qual;clustered_events;panel_of_normals;slippage;weak_evidence',\n", "       'fragment;map_qual;weak_evidence',\n", "       'base_qual;clustered_events;position;weak_evidence',\n", "       'multiallelic;panel_of_normals;weak_evidence',\n", "       'clustered_events;contamination;fragment;germline;panel_of_normals;weak_evidence',\n", "       'clustered_events;fragment;slippage;weak_evidence'], dtype=object)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df2[6].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "e036e723-478e-4661-9fd5-4b3bb165a55e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "651cd108-fc27-4a1c-a79e-bddd39b0ba96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "b4fbdf38-18b7-4433-a385-4f5a8c212ced", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>SAMPLE</th>\n", "      <th>patient</th>\n", "      <th>variant_id</th>\n", "      <th>population_classification</th>\n", "      <th>pathogenic_classification</th>\n", "      <th>variant_type</th>\n", "      <th>mutation_type</th>\n", "      <th>mutation_details</th>\n", "      <th>cgc_gene</th>\n", "      <th>is_cgc_gene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>3411794</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1112:8:393:14:2,391:1,189:1,202:-99:PASS:3...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_3411794_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>PRDM16</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>6887657</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>germline</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=167,146|111,1...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:313,215:0.4:528:102,59:86,66:197,131:167,1...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_6887657_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>CAMTA1</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>11121270</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=3;CSQ=T|synonymous_variant|LOW|M...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1552:9:517:13:0,517:0,237:0,280:-99:PASS:3...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_11121270_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>MTOR</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>11130589</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=3;CSQ=A|synonymous_variant|LOW|M...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:3035:14:1010:19:0,1010:0,509:0,501:-99:PAS...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_11130589_G_A</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>MTOR</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>13778644</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>clustered_events;germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=195,182|165,1...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:377,342:0.489:719:83,58:103,115:239,227:19...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_13778644_T_A</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>PRDM2</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107198</th>\n", "      <td>chr9</td>\n", "      <td>136513000</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=3,12|8,28;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:15,36:0.691:51:6,14:9,13:15,35:3,12,8,28</td>\n", "      <td>********</td>\n", "      <td>chr9_136513000_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>NOTCH1</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107199</th>\n", "      <td>chrX</td>\n", "      <td>45107550</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=7,5|13,21;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:12,34:0.711:46:7,8:2,14:10,26:7,5,13,21</td>\n", "      <td>********</td>\n", "      <td>chrX_45107550_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>KDM6A</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107200</th>\n", "      <td>chrX</td>\n", "      <td>45107560</td>\n", "      <td>.</td>\n", "      <td>GT</td>\n", "      <td>G</td>\n", "      <td>.</td>\n", "      <td>germline;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=7,5|12,22;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:12,34:0.73:46:6,8:2,13:9,26:7,5,12,22</td>\n", "      <td>********</td>\n", "      <td>chrX_45107560_GT_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>Indel</td>\n", "      <td>non-coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>KDM6A</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107201</th>\n", "      <td>chrX</td>\n", "      <td>77682661</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>germline</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=39,33|41,31;D...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:72,72:0.505:144:21,28:30,24:53,54:39,33,41,31</td>\n", "      <td>********</td>\n", "      <td>chrX_77682661_G_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>missense</td>\n", "      <td>ATRX</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107202</th>\n", "      <td>chrX</td>\n", "      <td>153549631</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>G</td>\n", "      <td>2723</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=4;CSQ=G|synonymous_variant|LOW|A...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:776:9:259:2:0,259:0,114:0,145:-99:PASS:370...</td>\n", "      <td>********</td>\n", "      <td>chrX_153549631_A_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "      <td>coding_region</td>\n", "      <td>synonymous</td>\n", "      <td>ATP2B3</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>107203 rows × 19 columns</p>\n", "</div>"], "text/plain": ["       CHROM        POS ID REF ALT  QUAL  \\\n", "0       chr1    3411794  .   T   C  3070   \n", "1       chr1    6887657  .   C   T     .   \n", "2       chr1   11121270  .   C   T  3070   \n", "3       chr1   11130589  .   G   A  3070   \n", "4       chr1   13778644  .   T   A     .   \n", "...      ...        ... ..  ..  ..   ...   \n", "107198  chr9  136513000  .   C   T     .   \n", "107199  chrX   45107550  .   T   C     .   \n", "107200  chrX   45107560  .  GT   G     .   \n", "107201  chrX   77682661  .   G   C     .   \n", "107202  chrX  153549631  .   A   G  2723   \n", "\n", "                                            FILTER  \\\n", "0                                             PASS   \n", "1                                         germline   \n", "2                                             PASS   \n", "3                                             PASS   \n", "4       clustered_events;germline;panel_of_normals   \n", "...                                            ...   \n", "107198                   germline;panel_of_normals   \n", "107199                   germline;panel_of_normals   \n", "107200                   germline;panel_of_normals   \n", "107201                                    germline   \n", "107202                                        PASS   \n", "\n", "                                                     INFO  \\\n", "0       MQ=60;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...   \n", "1       AS_FilterStatus=SITE;AS_SB_TABLE=167,146|111,1...   \n", "2       MQ=60;SNVHPOL=3;CSQ=T|synonymous_variant|LOW|M...   \n", "3       MQ=60;SNVHPOL=3;CSQ=A|synonymous_variant|LOW|M...   \n", "4       AS_FilterStatus=SITE;AS_SB_TABLE=195,182|165,1...   \n", "...                                                   ...   \n", "107198  AS_FilterStatus=SITE;AS_SB_TABLE=3,12|8,28;DP=...   \n", "107199  AS_FilterStatus=SITE;AS_SB_TABLE=7,5|13,21;DP=...   \n", "107200  AS_FilterStatus=SITE;AS_SB_TABLE=7,5|12,22;DP=...   \n", "107201  AS_FilterStatus=SITE;AS_SB_TABLE=39,33|41,31;D...   \n", "107202  MQ=60;SNVHPOL=4;CSQ=G|synonymous_variant|LOW|A...   \n", "\n", "                                      FORMAT  \\\n", "0       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "1               GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "2       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "3       GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "4               GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "...                                      ...   \n", "107198          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107199          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107200          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107201          GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "107202  GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "\n", "                                                   SAMPLE     patient  \\\n", "0       1/1:1112:8:393:14:2,391:1,189:1,202:-99:PASS:3...  P201103547   \n", "1       0/1:313,215:0.4:528:102,59:86,66:197,131:167,1...  P201103547   \n", "2       1/1:1552:9:517:13:0,517:0,237:0,280:-99:PASS:3...  P201103547   \n", "3       1/1:3035:14:1010:19:0,1010:0,509:0,501:-99:PAS...  P201103547   \n", "4       0/1:377,342:0.489:719:83,58:103,115:239,227:19...  P201103547   \n", "...                                                   ...         ...   \n", "107198       0/1:15,36:0.691:51:6,14:9,13:15,35:3,12,8,28    ********   \n", "107199        0/1:12,34:0.711:46:7,8:2,14:10,26:7,5,13,21    ********   \n", "107200          0/1:12,34:0.73:46:6,8:2,13:9,26:7,5,12,22    ********   \n", "107201  0/1:72,72:0.505:144:21,28:30,24:53,54:39,33,41,31    ********   \n", "107202  1/1:776:9:259:2:0,259:0,114:0,145:-99:PASS:370...    ********   \n", "\n", "                variant_id population_classification  \\\n", "0         chr1_3411794_T_C                Population   \n", "1         chr1_6887657_C_T                Population   \n", "2        chr1_11121270_C_T                Population   \n", "3        chr1_11130589_G_A                Population   \n", "4        chr1_13778644_T_A                Population   \n", "...                    ...                       ...   \n", "107198  chr9_136513000_C_T                Population   \n", "107199   chrX_45107550_T_C                Population   \n", "107200  chrX_45107560_GT_G                Population   \n", "107201   chrX_77682661_G_C                Population   \n", "107202  chrX_153549631_A_G                Population   \n", "\n", "       pathogenic_classification variant_type      mutation_type  \\\n", "0                  Likely benign          SNV      coding_region   \n", "1                  Likely benign          SNV  non-coding_region   \n", "2                  Likely benign          SNV      coding_region   \n", "3                  Likely benign          SNV      coding_region   \n", "4                  Likely benign          SNV      coding_region   \n", "...                          ...          ...                ...   \n", "107198             Likely benign          SNV  non-coding_region   \n", "107199             Likely benign          SNV  non-coding_region   \n", "107200             Likely benign        Indel  non-coding_region   \n", "107201             Likely benign          SNV      coding_region   \n", "107202             Likely benign          SNV      coding_region   \n", "\n", "       mutation_details cgc_gene  is_cgc_gene  \n", "0              missense   PRDM16         True  \n", "1            synonymous   CAMTA1         True  \n", "2            synonymous     MTOR         True  \n", "3            synonymous     MTOR         True  \n", "4              missense    PRDM2         True  \n", "...                 ...      ...          ...  \n", "107198       synonymous   NOTCH1         True  \n", "107199       synonymous    KDM6A         True  \n", "107200       synonymous    KDM6A         True  \n", "107201         missense     ATRX         True  \n", "107202       synonymous   ATP2B3         True  \n", "\n", "[107203 rows x 19 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/002-cgc_only_protein_impact.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "08f34146-4f1e-49ed-801d-41e80dee0c95", "metadata": {}, "outputs": [], "source": ["vep_fields = ['Allele', 'Consequence', 'IMPACT', 'SYMB<PERSON>', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON', 'HGVSc', 'HGVSp', 'cDNA_position', 'CDS_position', 'Protein_position', 'Amino_acids', 'Codons', 'Existing_variation', 'DISTANCE', 'STRAND', 'FLAGS', 'VARIANT_CLASS', 'SYMBOL_SOURCE', 'HGNC_ID', 'CANONICAL', 'MANE_SELECT', 'MANE_PLUS_CLINICAL', 'TSL', 'APPRIS', 'CCDS', 'ENSP', 'SWISSPROT', 'TREMBL', 'UNIPARC', 'UNIPROT_ISOFORM', 'GENE_PHENO', 'SIFT', 'PolyPhen', 'DOMAINS', 'miRNA', 'AF', 'AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'gnomADe_AF', 'gnomADe_AFR_AF', 'gnomADe_AMR_AF', 'gnomADe_ASJ_AF', 'gnomADe_EAS_AF', 'gnomADe_FIN_AF', 'gnomADe_NFE_AF', 'gnomADe_OTH_AF', 'gnomADe_SAS_AF', 'gnomADg_AF', 'gnomADg_AFR_AF', 'gnomADg_AMI_AF', 'gnomADg_AMR_AF', 'gnomADg_ASJ_AF', 'gnomADg_EAS_AF', 'gnomADg_FIN_AF', 'gnomADg_MID_AF', 'gnomADg_NFE_AF', 'gnomADg_OTH_AF', 'gnomADg_SAS_AF', 'MAX_AF', 'MAX_AF_POPS', 'FREQS', 'CLIN_SIG', 'SOMATIC', 'PHENO', 'PUBMED', 'MOTIF_NAME', 'MOTIF_POS', 'HIGH_INF_POS', 'MOTIF_SCORE_CHANGE', 'TRANSCRIPTION_FACTORS']\n"]}, {"cell_type": "code", "execution_count": 4, "id": "69dd3f87-3cf7-4e7b-a2bc-32990dd5fa4c", "metadata": {}, "outputs": [], "source": ["# df['FILTER'].unique()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "03ae646d-b971-495d-90f6-197a2f9e43e7", "metadata": {}, "outputs": [], "source": ["df['INFO']"]}, {"cell_type": "code", "execution_count": null, "id": "582c1f20-b674-4625-b225-5b892b963e07", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bca94608-f8cf-4985-8743-4da82e0c80d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7334d989-26eb-4486-bcd2-4b3e0679e2a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "11813fb8-36b6-4c23-8d2f-cfd78a5baa4b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>SAMPLE</th>\n", "      <th>patient</th>\n", "      <th>variant_id</th>\n", "      <th>population_classification</th>\n", "      <th>pathogenic_classification</th>\n", "      <th>variant_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>69511</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>G</td>\n", "      <td>2991</td>\n", "      <td>PASS</td>\n", "      <td>MQ=41;SNVHPOL=3;CSQ=G|missense_variant|MODERAT...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:818:7:273:4:0,273:0,160:0,113:-99:PASS:370...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_69511_A_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>952421</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>G</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=3;CSQ=G|synonymous_variant|LOW|N...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1005:9:335:4:0,335:0,190:0,145:-99:PASS:37...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_952421_A_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>953259</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>2802</td>\n", "      <td>PASS</td>\n", "      <td>MQ=59;SNVHPOL=2;CSQ=C|synonymous_variant|LOW|N...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:704:8:235:9:0,235:0,101:0,134:-99:PASS:370...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_953259_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>953279</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>2110</td>\n", "      <td>PASS</td>\n", "      <td>MQ=59;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:527:8:176:7:0,176:0,67:0,109:-99:PASS:370,...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_953279_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>960684</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>G</td>\n", "      <td>.</td>\n", "      <td>clustered_events;germline;haplotype</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=19,24|16,22;D...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:PGT:PID:PS:SB</td>\n", "      <td>0|1:43,38:0.49:81:8,8:13,9:24,23:0|1:960684_C_...</td>\n", "      <td>P201103547</td>\n", "      <td>chr1_960684_C_G</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9995</th>\n", "      <td>chr22</td>\n", "      <td>50283979</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>germline</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=70,87|108,135...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:SB</td>\n", "      <td>0/1:157,243:0.605:400:52,79:47,71:105,161:70,8...</td>\n", "      <td>P201103547</td>\n", "      <td>chr22_50283979_T_C</td>\n", "      <td>Population</td>\n", "      <td>Likely damaging</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9996</th>\n", "      <td>chr22</td>\n", "      <td>50285761</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>germline;haplotype;panel_of_normals</td>\n", "      <td>AS_FilterStatus=SITE;AS_SB_TABLE=1,1|43,39;DP=...</td>\n", "      <td>GT:AD:AF:DP:F1R2:F2R1:FAD:PGT:PID:PS:SB</td>\n", "      <td>0|1:2,82:0.962:84:1,23:0,24:2,55:0|1:50285743_...</td>\n", "      <td>P201103547</td>\n", "      <td>chr22_50285761_G_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9997</th>\n", "      <td>chr22</td>\n", "      <td>50313100</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>C</td>\n", "      <td>2740</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=7;CSQ=C|synonymous_variant|LOW|D...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:710:9:251:2:0,249:0,98:0,151:-99:PASS:370,...</td>\n", "      <td>P201103547</td>\n", "      <td>chr22_50313100_G_C</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9998</th>\n", "      <td>chr22</td>\n", "      <td>50313721</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=2;CSQ=T|upstream_gene_variant|MO...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:911:8:304:36:0,304:0,167:0,137:-99:PASS:37...</td>\n", "      <td>P201103547</td>\n", "      <td>chr22_50313721_C_T</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9999</th>\n", "      <td>chr22</td>\n", "      <td>50314905</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>3070</td>\n", "      <td>PASS</td>\n", "      <td>MQ=60;SNVHPOL=2;CSQ=A|regulatory_region_varian...</td>\n", "      <td>GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL</td>\n", "      <td>1/1:1062:9:354:3:0,354:0,181:0,173:-99:PASS:37...</td>\n", "      <td>P201103547</td>\n", "      <td>chr22_50314905_G_A</td>\n", "      <td>Population</td>\n", "      <td>Likely benign</td>\n", "      <td>SNV</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10000 rows × 15 columns</p>\n", "</div>"], "text/plain": ["      CHROM       POS ID REF ALT  QUAL                               FILTER  \\\n", "0      chr1     69511  .   A   G  2991                                 PASS   \n", "1      chr1    952421  .   A   G  3070                                 PASS   \n", "2      chr1    953259  .   T   C  2802                                 PASS   \n", "3      chr1    953279  .   T   C  2110                                 PASS   \n", "4      chr1    960684  .   C   G     .  clustered_events;germline;haplotype   \n", "...     ...       ... ..  ..  ..   ...                                  ...   \n", "9995  chr22  50283979  .   T   C     .                             germline   \n", "9996  chr22  50285761  .   G   T     .  germline;haplotype;panel_of_normals   \n", "9997  chr22  50313100  .   G   C  2740                                 PASS   \n", "9998  chr22  50313721  .   C   T  3070                                 PASS   \n", "9999  chr22  50314905  .   G   A  3070                                 PASS   \n", "\n", "                                                   INFO  \\\n", "0     MQ=41;SNVHPOL=3;CSQ=G|missense_variant|MODERAT...   \n", "1     MQ=60;SNVHPOL=3;CSQ=G|synonymous_variant|LOW|N...   \n", "2     MQ=59;SNVHPOL=2;CSQ=C|synonymous_variant|LOW|N...   \n", "3     MQ=59;SNVHPOL=2;CSQ=C|missense_variant|MODERAT...   \n", "4     AS_FilterStatus=SITE;AS_SB_TABLE=19,24|16,22;D...   \n", "...                                                 ...   \n", "9995  AS_FilterStatus=SITE;AS_SB_TABLE=70,87|108,135...   \n", "9996  AS_FilterStatus=SITE;AS_SB_TABLE=1,1|43,39;DP=...   \n", "9997  MQ=60;SNVHPOL=7;CSQ=C|synonymous_variant|LOW|D...   \n", "9998  MQ=60;SNVHPOL=2;CSQ=T|upstream_gene_variant|MO...   \n", "9999  MQ=60;SNVHPOL=2;CSQ=A|regulatory_region_varian...   \n", "\n", "                                       FORMAT  \\\n", "0        GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "1        GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "2        GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "3        GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "4     GT:AD:AF:DP:F1R2:F2R1:FAD:PGT:PID:PS:SB   \n", "...                                       ...   \n", "9995             GT:AD:AF:DP:F1R2:F2R1:FAD:SB   \n", "9996  GT:AD:AF:DP:F1R2:F2R1:FAD:PGT:PID:PS:SB   \n", "9997     GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "9998     GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "9999     GT:GQ:GQX:DP:DPF:AD:ADF:ADR:SB:FT:PL   \n", "\n", "                                                 SAMPLE     patient  \\\n", "0     1/1:818:7:273:4:0,273:0,160:0,113:-99:PASS:370...  P201103547   \n", "1     1/1:1005:9:335:4:0,335:0,190:0,145:-99:PASS:37...  P201103547   \n", "2     1/1:704:8:235:9:0,235:0,101:0,134:-99:PASS:370...  P201103547   \n", "3     1/1:527:8:176:7:0,176:0,67:0,109:-99:PASS:370,...  P201103547   \n", "4     0|1:43,38:0.49:81:8,8:13,9:24,23:0|1:960684_C_...  P201103547   \n", "...                                                 ...         ...   \n", "9995  0/1:157,243:0.605:400:52,79:47,71:105,161:70,8...  P201103547   \n", "9996  0|1:2,82:0.962:84:1,23:0,24:2,55:0|1:50285743_...  P201103547   \n", "9997  1/1:710:9:251:2:0,249:0,98:0,151:-99:PASS:370,...  P201103547   \n", "9998  1/1:911:8:304:36:0,304:0,167:0,137:-99:PASS:37...  P201103547   \n", "9999  1/1:1062:9:354:3:0,354:0,181:0,173:-99:PASS:37...  P201103547   \n", "\n", "              variant_id population_classification pathogenic_classification  \\\n", "0         chr1_69511_A_G                Population             Likely benign   \n", "1        chr1_952421_A_G                Population             Likely benign   \n", "2        chr1_953259_T_C                Population             Likely benign   \n", "3        chr1_953279_T_C                Population             Likely benign   \n", "4        chr1_960684_C_G                Population             Likely benign   \n", "...                  ...                       ...                       ...   \n", "9995  chr22_50283979_T_C                Population           Likely damaging   \n", "9996  chr22_50285761_G_T                Population             Likely benign   \n", "9997  chr22_50313100_G_C                Population             Likely benign   \n", "9998  chr22_50313721_C_T                Population             Likely benign   \n", "9999  chr22_50314905_G_A                Population             Likely benign   \n", "\n", "     variant_type  \n", "0             SNV  \n", "1             SNV  \n", "2             SNV  \n", "3             SNV  \n", "4             SNV  \n", "...           ...  \n", "9995          SNV  \n", "9996          SNV  \n", "9997          SNV  \n", "9998          SNV  \n", "9999          SNV  \n", "\n", "[10000 rows x 15 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/002-annotated_variants_vcf_0.01.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t', nrows=10000)\n", "\n", "df\n"]}, {"cell_type": "code", "execution_count": 6, "id": "6a7f590c-5919-445a-8c25-a7d034fc5ba7", "metadata": {}, "outputs": [{"data": {"text/plain": ["0         OR4F5\n", "1         NOC2L\n", "2         NOC2L\n", "3         NOC2L\n", "4              \n", "         ...   \n", "9995     PLXNB2\n", "9996     PLXNB2\n", "9997    DENND6B\n", "9998           \n", "9999           \n", "Name: INFO, Length: 10000, dtype: object"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["res = df['INFO'].str.split(\";\").str[-1]\n", "res = res.str.split(\"|\").str[3]\n", "\n", "res"]}, {"cell_type": "code", "execution_count": 17, "id": "bfbf4aa0-b817-47d6-a0f0-54761255c5a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['inframe_insertion', 'inframe_deletion', 'frameshift_variant',\n", "       'frameshift_variant&splice_donor_region_variant'], dtype=object)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 'frame'\n", "# 'sense'\n", "# 'synon'\n", "# 'gain'\n", "# 'lost'\n", "# 'prot'\n", "\n", "# 'frame|sense|synon|gain|lost|prot'\n", "\n", "res.unique()[pd.Series(res.unique()).str.contains('frame')]"]}, {"cell_type": "code", "execution_count": 14, "id": "ee3a207b-eb3f-451d-9f73-afbec72db33f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['missense_variant', 'synonymous_variant',\n", "       'regulatory_region_variant', 'downstream_gene_variant',\n", "       'intron_variant',\n", "       'splice_polypyrimidine_tract_variant&intron_variant',\n", "       'upstream_gene_variant',\n", "       'splice_donor_region_variant&intron_variant',\n", "       'missense_variant&splice_region_variant',\n", "       'splice_region_variant&intron_variant', '5_prime_UTR_variant',\n", "       'inframe_insertion',\n", "       'splice_donor_5th_base_variant&intron_variant&non_coding_transcript_variant',\n", "       'non_coding_transcript_exon_variant',\n", "       'splice_region_variant&splice_polypyrimidine_tract_variant&intron_variant',\n", "       '3_prime_UTR_variant',\n", "       'intron_variant&non_coding_transcript_variant',\n", "       'TF_binding_site_variant', 'inframe_deletion',\n", "       'stop_gained&splice_region_variant', 'start_lost',\n", "       'frameshift_variant', 'stop_gained', 'splice_acceptor_variant',\n", "       'splice_region_variant&synonymous_variant',\n", "       'splice_donor_5th_base_variant&intron_variant',\n", "       'stop_retained_variant', 'stop_lost',\n", "       '3_prime_UTR_variant&NMD_transcript_variant',\n", "       'splice_polypyrimidine_tract_variant&intron_variant&non_coding_transcript_variant',\n", "       'intergenic_variant',\n", "       'splice_region_variant&splice_polypyrimidine_tract_variant&intron_variant&non_coding_transcript_variant',\n", "       'splice_region_variant&non_coding_transcript_exon_variant',\n", "       'splice_donor_variant&non_coding_transcript_variant',\n", "       'intron_variant&NMD_transcript_variant',\n", "       'splice_donor_region_variant&intron_variant&non_coding_transcript_variant',\n", "       'frameshift_variant&splice_donor_region_variant',\n", "       'splice_donor_variant', 'protein_altering_variant'], dtype=object)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["res.unique()"]}, {"cell_type": "code", "execution_count": 2, "id": "a51f02c6-0ccd-4d80-b06e-e8a2a0eeadff", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>key</th>\n", "      <th>value</th>\n", "      <th>patient</th>\n", "      <th>variant_caller</th>\n", "      <th>source</th>\n", "      <th>bam</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>samples</td>\n", "      <td>1</td>\n", "      <td>P201103547</td>\n", "      <td>strelka2</td>\n", "      <td>PERH2-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>cords</td>\n", "      <td>9682</td>\n", "      <td>P201103547</td>\n", "      <td>strelka2</td>\n", "      <td>PERH2-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-ALTs</td>\n", "      <td>0</td>\n", "      <td>P201103547</td>\n", "      <td>strelka2</td>\n", "      <td>PERH2-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SNPs</td>\n", "      <td>9646</td>\n", "      <td>P201103547</td>\n", "      <td>strelka2</td>\n", "      <td>PERH2-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MNPs</td>\n", "      <td>0</td>\n", "      <td>P201103547</td>\n", "      <td>strelka2</td>\n", "      <td>PERH2-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5665</th>\n", "      <td>MNPs</td>\n", "      <td>115</td>\n", "      <td>********</td>\n", "      <td>mutect2</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5666</th>\n", "      <td>indels</td>\n", "      <td>794</td>\n", "      <td>********</td>\n", "      <td>mutect2</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5667</th>\n", "      <td>thers</td>\n", "      <td>0</td>\n", "      <td>********</td>\n", "      <td>mutect2</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5668</th>\n", "      <td>ltiallelic sites</td>\n", "      <td>41</td>\n", "      <td>********</td>\n", "      <td>mutect2</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5669</th>\n", "      <td>ltiallelic SNP sites</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>mutect2</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5670 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                       key  value     patient variant_caller  \\\n", "0                  samples      1  P201103547       strelka2   \n", "1                    cords   9682  P201103547       strelka2   \n", "2                    -ALTs      0  P201103547       strelka2   \n", "3                     SNPs   9646  P201103547       strelka2   \n", "4                     MNPs      0  P201103547       strelka2   \n", "...                    ...    ...         ...            ...   \n", "5665                  MNPs    115    ********        mutect2   \n", "5666                indels    794    ********        mutect2   \n", "5667                 thers      0    ********        mutect2   \n", "5668      ltiallelic sites     41    ********        mutect2   \n", "5669  ltiallelic SNP sites      6    ********        mutect2   \n", "\n", "                     source                                                bam  \n", "0         PERH2-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "1         PERH2-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "2         PERH2-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "3         PERH2-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "4         PERH2-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "...                     ...                                                ...  \n", "5665  PERH_data-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "5666  PERH_data-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "5667  PERH_data-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "5668  PERH_data-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "5669  PERH_data-BAMS-ENC_p2  /.mounts/labs/reimandlab/private/generated_raw...  \n", "\n", "[5670 rows x 6 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/001-vcf_summary.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t')\n", "df\n"]}, {"cell_type": "code", "execution_count": null, "id": "5a4d9ff9-b123-4980-a3d3-8eb41cb5c01e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "7d54c4ca-b854-4063-97da-057d420fec79", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Samples</th>\n", "      <th>Recurrence type</th>\n", "      <th>Average sequenced coverage over target region</th>\n", "      <th>SNPs before filtering</th>\n", "      <th>SNPs after filtering</th>\n", "      <th>INDELs before filtering</th>\n", "      <th>INDELs after filtering</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Samples</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>410482</th>\n", "      <td>0410482__6_V_vg</td>\n", "      <td>NR</td>\n", "      <td>1360.50</td>\n", "      <td>28001</td>\n", "      <td>2497</td>\n", "      <td>1504</td>\n", "      <td>173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410487</th>\n", "      <td>0410487__93_II</td>\n", "      <td>NR</td>\n", "      <td>855.59</td>\n", "      <td>27798</td>\n", "      <td>2017</td>\n", "      <td>1435</td>\n", "      <td>173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>415218</th>\n", "      <td>0415218__22_III</td>\n", "      <td>NR</td>\n", "      <td>963.91</td>\n", "      <td>27821</td>\n", "      <td>2101</td>\n", "      <td>1299</td>\n", "      <td>179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45090</th>\n", "      <td>045090__8_II</td>\n", "      <td>NR</td>\n", "      <td>1119.23</td>\n", "      <td>27076</td>\n", "      <td>1894</td>\n", "      <td>1404</td>\n", "      <td>147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>518400</th>\n", "      <td>0518400__12_X</td>\n", "      <td>NR</td>\n", "      <td>699.73</td>\n", "      <td>28397</td>\n", "      <td>2563</td>\n", "      <td>1355</td>\n", "      <td>147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>X201700179</th>\n", "      <td>X201700179_5P4</td>\n", "      <td>R</td>\n", "      <td>1200.48</td>\n", "      <td>31405</td>\n", "      <td>6753</td>\n", "      <td>7329</td>\n", "      <td>5354</td>\n", "    </tr>\n", "    <tr>\n", "      <th>X201701413</th>\n", "      <td>X201701413_1_P1</td>\n", "      <td>R</td>\n", "      <td>712.08</td>\n", "      <td>28443</td>\n", "      <td>3599</td>\n", "      <td>2424</td>\n", "      <td>542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>X201702805</th>\n", "      <td>X201702805_8P3</td>\n", "      <td>R</td>\n", "      <td>977.42</td>\n", "      <td>30337</td>\n", "      <td>5770</td>\n", "      <td>5837</td>\n", "      <td>3707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>X201721754</th>\n", "      <td>X201721754_1P2</td>\n", "      <td>R</td>\n", "      <td>355.33</td>\n", "      <td>32071</td>\n", "      <td>4238</td>\n", "      <td>4662</td>\n", "      <td>2274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZZ04911</th>\n", "      <td>ZZZ04911_2</td>\n", "      <td>R</td>\n", "      <td>510.11</td>\n", "      <td>28182</td>\n", "      <td>4501</td>\n", "      <td>4842</td>\n", "      <td>2790</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>165 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                    Samples Recurrence type  \\\n", "<PERSON><PERSON>                                       \n", "410482      0410482__6_V_vg              NR   \n", "410487       0410487__93_II              NR   \n", "415218      0415218__22_III              NR   \n", "45090          045090__8_II              NR   \n", "518400        0518400__12_X              NR   \n", "...                     ...             ...   \n", "X201700179   X201700179_5P4               R   \n", "X201701413  X201701413_1_P1               R   \n", "X201702805   X201702805_8P3               R   \n", "X201721754   X201721754_1P2               R   \n", "ZZZ04911         ZZZ04911_2               R   \n", "\n", "            Average sequenced coverage over target region  \\\n", "<PERSON><PERSON>                                                     \n", "410482                                            1360.50   \n", "410487                                             855.59   \n", "415218                                             963.91   \n", "45090                                             1119.23   \n", "518400                                             699.73   \n", "...                                                   ...   \n", "X201700179                                        1200.48   \n", "X201701413                                         712.08   \n", "X201702805                                         977.42   \n", "X201721754                                         355.33   \n", "ZZZ04911                                           510.11   \n", "\n", "            SNPs before filtering  SNPs after filtering  \\\n", "<PERSON><PERSON>                                                   \n", "410482                      28001                  2497   \n", "410487                      27798                  2017   \n", "415218                      27821                  2101   \n", "45090                       27076                  1894   \n", "518400                      28397                  2563   \n", "...                           ...                   ...   \n", "X201700179                  31405                  6753   \n", "X201701413                  28443                  3599   \n", "X201702805                  30337                  5770   \n", "X201721754                  32071                  4238   \n", "ZZZ04911                    28182                  4501   \n", "\n", "            INDELs before filtering  INDELs after filtering  \n", "<PERSON><PERSON>                                                      \n", "410482                         1504                     173  \n", "410487                         1435                     173  \n", "415218                         1299                     179  \n", "45090                          1404                     147  \n", "518400                         1355                     147  \n", "...                             ...                     ...  \n", "X201700179                     7329                    5354  \n", "X201701413                     2424                     542  \n", "X201702805                     5837                    3707  \n", "X201721754                     4662                    2274  \n", "ZZZ04911                       4842                    2790  \n", "\n", "[165 rows x 7 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/2023_december_12_NR_and_R_patient_data.csv'\n", "\n", "df2 = pd.read_csv(file)\n", "samples = df2['Samples'].str.split(\"_\").str[0].str.lstrip(\"0\")\n", "df2.index = samples\n", "\n", "df2"]}, {"cell_type": "code", "execution_count": 13, "id": "b3cb4552-c420-4994-a8c9-63d2428275a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Samples                                          ********\n", "Recurrence type                                         R\n", "Average sequenced coverage over target region     1145.25\n", "SNPs before filtering                               31774\n", "SNPs after filtering                                 6191\n", "INDELs before filtering                              4269\n", "INDELs after filtering                               2114\n", "Name: ********, dtype: object"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.loc['********',:]"]}, {"cell_type": "code", "execution_count": null, "id": "9874a842-b2d0-480f-a56f-9802f2e88042", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae3d2b15-0eb3-4528-acd8-2f6020ecf603", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "82a3e06a-bef7-4756-a932-c8556633af0a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient</th>\n", "      <th>sample</th>\n", "      <th>vcf</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1008</td>\n", "      <td>1008-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1009</td>\n", "      <td>1009-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1058</td>\n", "      <td>1058-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>106</td>\n", "      <td>106-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>107</td>\n", "      <td>107-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>X201702805</td>\n", "      <td>X201702805-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>X201721754</td>\n", "      <td>X201721754-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>X201800915</td>\n", "      <td>X201800915-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>X201916389</td>\n", "      <td>X201916389-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>ZZZ04911</td>\n", "      <td>ZZZ04911-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>209 rows × 3 columns</p>\n", "</div>"], "text/plain": ["        patient            sample  \\\n", "0          1008        1008-tumor   \n", "1          1009        1009-tumor   \n", "2          1058        1058-tumor   \n", "3           106         106-tumor   \n", "4           107         107-tumor   \n", "..          ...               ...   \n", "204  X201702805  X201702805-tumor   \n", "205  X201721754  X201721754-tumor   \n", "206  X201800915  X201800915-tumor   \n", "207  X201916389  X201916389-tumor   \n", "208    ZZZ04911    ZZZ04911-tumor   \n", "\n", "                                                   vcf  \n", "0    /.mounts/labs/reimandlab/private/users/abahche...  \n", "1    /.mounts/labs/reimandlab/private/users/abahche...  \n", "2    /.mounts/labs/reimandlab/private/users/abahche...  \n", "3    /.mounts/labs/reimandlab/private/users/abahche...  \n", "4    /.mounts/labs/reimandlab/private/users/abahche...  \n", "..                                                 ...  \n", "204  /.mounts/labs/reimandlab/private/users/abahche...  \n", "205  /.mounts/labs/reimandlab/private/users/abahche...  \n", "206  /.mounts/labs/reimandlab/private/users/abahche...  \n", "207  /.mounts/labs/reimandlab/private/users/abahche...  \n", "208  /.mounts/labs/reimandlab/private/users/abahche...  \n", "\n", "[209 rows x 3 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["samples = [\"1008\", \"1009\", \"1058\", \"106\", \"107\", \"108\", \"115\", \"1284\", \"146\", \"153\", \"157\", \"158\", \"15936\", \"257\", \"294\", \"300\", \"301\", \"302\", \"303\", \"304\", \"305\", \"343\", \"344\", \"354\", \"363\", \"364\", \"384\", \"396\", \"397\", \"410482\", \"410487\", \"415218\", \"427\", \"45090\", \"518400\", \"54969\", \"56185\", \"58004\", \"58669\", \"611715\", \"612509\", \"615164\", \"61549\", \"618466\", \"618925\", \"619189\", \"63242\", \"64224\", \"64647\", \"64815\", \"67639\", \"69393\", \"710223\", \"711005\", \"711305\", \"711441\", \"712599\", \"718976\", \"72017\", \"73310\", \"74306\", \"749\", \"750\", \"79228\", \"79856\", \"805595\", \"811989\", \"816174\", \"817344\", \"818318\", \"859\", \"860\", \"892\", \"901289\", \"901826\", \"904684\", \"905\", \"905344\", \"906\", \"91\", \"916285\", \"918500\", \"927\", \"94\", \"941\", \"IVKH\", \"M1207987\", \"********\", \"M1400519\", \"M1402871\", \"M1502315\", \"M201101474\", \"M201102498\", \"M201109265\", \"M201109514\", \"M201200852\", \"M201201464\", \"M201202994\", \"M201207597\", \"M201208496\", \"M201301448\", \"M201301639\", \"M201302364\", \"M201305657\", \"M201305817\", \"M201305818\", \"M201305986\", \"M201307747\", \"M201308134\", \"M201309141\", \"M201309783\", \"M201400218\", \"M201400880\", \"M201401097\", \"M201401285\", \"M201401950\", \"M201402017\", \"M201402485\", \"M201402870\", \"M201403436\", \"M201403572\", \"M201403672\", \"M201404036\", \"M201404144\", \"M201404940\", \"M201405126\", \"M201405277\", \"M201405596\", \"M201406061\", \"M201406184\", \"M201500596\", \"M201500632\", \"M201501276\", \"M201501320\", \"M201501554\", \"M201501652\", \"M201501653\", \"M201502042_A\", \"M201502042_B\", \"M201503294\", \"M201504055\", \"M201504156\", \"M201504347\", \"M201505317\", \"M201505598\", \"M201505599\", \"M201506208\", \"M201506834\", \"M201507012\", \"M201507466\", \"M201507993\", \"M201508410\", \"M201508988\", \"MZZ0Z755\", \"O201009049\", \"O201012423\", \"O201012924\", \"P1400242\", \"********\", \"P1409332\", \"P201103401\", \"P201103547\", \"P201110327\", \"P201110566\", \"P201209910\", \"P201300967\", \"P201303028\", \"P201303501\", \"P201305237\", \"P201306082\", \"P201306312\", \"P201306634\", \"P201306887\", \"P201309335\", \"P201310059\", \"P201400025\", \"P201402751\", \"P201406539\", \"P201406675\", \"P201409588\", \"P201409797\", \"P201413005\", \"P201500190\", \"P201500292\", \"P201501442\", \"P201504782\", \"P201509880\", \"X201600572\", \"X201602143\", \"X201602525\", \"X201605202\", \"X201605586\", \"X201605838\", \"X201609690\", \"X201610295\", \"X201610732\", \"X201610893_1\", \"X201610893_2\", \"X201614374\", \"X201617428\", \"X201617706\", \"X201700179\", \"X201701399\", \"X201701413\", \"X201702805\", \"X201721754\", \"X201800915\", \"X201916389\", \"ZZZ04911\"]\n", "\n", "dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_vcfs/results'\n", "\n", "df = []\n", "\n", "for sample in samples:\n", "    vcf_file = os.path.join(dir, f'{sample}-tumor_combined.vcf.gz')\n", "    df.append([sample, f\"{sample}-tumor\", vcf_file])\n", "\n", "df = pd.DataFrame(df)\n", "df.columns = 'patient,sample,vcf'.split(\",\")\n", "\n", "df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek_variants/sarek_combined_annotations.csv\", index=False)\n", "\n", "df\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ef6515b-131e-462a-9c90-7f0ff091a5f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 41, "id": "8529def8-e820-47e1-94cc-147f7f61a2c1", "metadata": {}, "outputs": [], "source": ["destination_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_variant_calling/bam_files'\n", "variant_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_variant_calling/variant_calling'\n", "\n", "\n", "files = main_df['bam']\n", "patients = list(main_df['patient'])\n", "\n", "samples_list = []\n", "\n", "for i, file in enumerate(files):\n", "    new_name = file.split(\"/\")[-1].split(\"_\")[0].lstrip('0')\n", "    samples_list.append(new_name)\n", "    \n", "    # new file path\n", "    new_file = os.path.join(destination_dir, f'{new_name}.bam')\n", "\n", "    # create link if it doesn't exist\n", "    if not os.path.exists(new_file):\n", "        os.symlink(file, new_file)\n", "\n", "    directory = os.path.join(variant_dir, new_name)\n", "    if not os.path.exists(directory):\n", "        os.makedirs(directory)\n", "\n", "    null_file_path = os.path.join(directory, 'null.txt')\n", "    if not os.path.exists(null_file_path):\n", "        with open(null_file_path, 'w') as null_file:\n", "            pass\n", "\n", "    directory = os.path.join(variant_dir, new_name, 'mutect2')\n", "    if not os.path.exists(directory):\n", "        os.makedirs(directory)\n", "\n", "    null_file_path = os.path.join(directory, 'null.txt')\n", "    if not os.path.exists(null_file_path):\n", "        with open(null_file_path, 'w') as null_file:\n", "            pass\n", "\n", "\n", "    \n", "\n"]}, {"cell_type": "code", "execution_count": 40, "id": "0ca2621b-7b7d-422f-b0a8-3fd6cd2b05ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["'15936\", \"410482\", \"410487\", \"415218\", \"45090\", \"518400\", \"54969\", \"56185\", \"58004\", \"58669\", \"611715\", \"612509\", \"615164\", \"61549\", \"618466\", \"618925\", \"619189\", \"63242\", \"64224\", \"64647\", \"64815\", \"67639\", \"69393\", \"710223\", \"711005\", \"711305\", \"711441\", \"712599\", \"718976\", \"72017\", \"73310\", \"74306\", \"79228\", \"79856\", \"805595\", \"811989\", \"816174\", \"817344\", \"818318\", \"901289\", \"901826\", \"904684\", \"905344\", \"916285\", \"918500\", \"IVKH\", \"M1207987\", \"********\", \"M1400519\", \"M1402871\", \"M1502315\", \"M201101474\", \"M201102498\", \"M201109265\", \"M201109514\", \"M201200852\", \"M201201464\", \"M201202994\", \"M201207597\", \"M201208496\", \"M201301448\", \"M201301639\", \"M201302364\", \"M201305657\", \"M201305817\", \"M201305818\", \"M201305986\", \"M201307747\", \"M201308134\", \"M201309141\", \"M201309783\", \"M201400218\", \"M201400880\", \"M201401097\", \"M201401285\", \"M201401950\", \"M201402017\", \"M201402485\", \"M201402870\", \"M201403436\", \"M201403572\", \"M201403672\", \"M201404036\", \"M201404144\", \"M201404940\", \"M201405126\", \"M201405277\", \"M201405596\", \"M201406061\", \"M201406184\", \"M201500596\", \"M201500632\", \"M201501276\", \"M201501320\", \"M201501554\", \"M201501652\", \"M201501653\", \"M201502042\", \"M201503294\", \"M201504055\", \"M201504156\", \"M201504347\", \"M201505317\", \"M201505598\", \"M201505599\", \"M201506208\", \"M201506834\", \"M201507012\", \"M201507466\", \"M201507993\", \"M201508410\", \"M201508988\", \"MZZ0Z755\", \"O201009049\", \"O201012423\", \"O201012924\", \"P1400242\", \"********\", \"P1409332\", \"P201103401\", \"P201103547\", \"P201110327\", \"P201110566\", \"P201209910\", \"P201300967\", \"P201303028\", \"P201303501\", \"P201305237\", \"P201306082\", \"P201306312\", \"P201306634\", \"P201306887\", \"P201309335\", \"P201310059\", \"P201400025\", \"P201402751\", \"P201406539\", \"P201406675\", \"P201409588\", \"P201409797\", \"P201413005\", \"P201500190\", \"P201500292\", \"P201501442\", \"P201504782\", \"P201509880\", \"X201600572\", \"X201602143\", \"X201602525\", \"X201605202\", \"X201605586\", \"X201605838\", \"X201609690\", \"X201610295\", \"X201610732\", \"X201610893\", \"X201614374\", \"X201617428\", \"X201617706\", \"X201700179\", \"X201701399\", \"X201701413\", \"X201702805\", \"X201721754\", \"X201800915\", \"X201916389\", \"ZZZ04911'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["'\", \"'.join(np.unique(samples_list))"]}, {"cell_type": "code", "execution_count": null, "id": "04860829-62c6-4684-ad66-068f55554a6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "daf64afb-089a-49d0-a3fe-ae1f52abc9ae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "2d39875a-ea1e-442a-a3e7-383edd364fdb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["206\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient</th>\n", "      <th>source</th>\n", "      <th>bam</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>M201502042</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>58669</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>615164</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>712599</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>916285</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>P201501442</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>P201509880</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>X201605586</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>X201610295</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZZZ04911</td>\n", "      <td>PERH_data-BAMS-ENC</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>167 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       patient                 source  \\\n", "0   M201502042      PERH2-BAMS-ENC_p1   \n", "2        58669      PERH2-BAMS-ENC_p1   \n", "3       615164      PERH2-BAMS-ENC_p1   \n", "4       712599      PERH2-BAMS-ENC_p1   \n", "5       916285      PERH2-BAMS-ENC_p1   \n", "..         ...                    ...   \n", "36  P201501442  PERH_data-BAMS-ENC_p2   \n", "37  P201509880  PERH_data-BAMS-ENC_p2   \n", "38  X201605586  PERH_data-BAMS-ENC_p2   \n", "39  X201610295  PERH_data-BAMS-ENC_p2   \n", "0     ZZZ04911     PERH_data-BAMS-ENC   \n", "\n", "                                                  bam  \n", "0   /.mounts/labs/reimandlab/private/generated_raw...  \n", "2   /.mounts/labs/reimandlab/private/generated_raw...  \n", "3   /.mounts/labs/reimandlab/private/generated_raw...  \n", "4   /.mounts/labs/reimandlab/private/generated_raw...  \n", "5   /.mounts/labs/reimandlab/private/generated_raw...  \n", "..                                                ...  \n", "36  /.mounts/labs/reimandlab/private/generated_raw...  \n", "37  /.mounts/labs/reimandlab/private/generated_raw...  \n", "38  /.mounts/labs/reimandlab/private/generated_raw...  \n", "39  /.mounts/labs/reimandlab/private/generated_raw...  \n", "0   /.mounts/labs/reimandlab/private/generated_raw...  \n", "\n", "[167 rows x 3 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek'\n", "files = ['sarek_decrypted-data-DNA-ENC.csv', 'sarek_PERH2-BAMS-ENC_p1.csv', 'sarek_PERH2-BAMS-ENC_p2.csv', 'sarek_PERH_data-BAMS-ENC_p1.csv', 'sarek_PERH_data-BAMS-ENC_p2.csv', 'zsample.csv']\n", "\n", "\n", "# Initialize an empty list to store the first columns\n", "first_columns = []\n", "\n", "res_df = []\n", "\n", "# Loop through each file\n", "for file in files:\n", "    # Load the DataFrame from the file\n", "    df = pd.read_csv(os.path.join(main_dir, file))\n", "    \n", "    # Assuming the first column name is 'column_name'\n", "    first_column = df.iloc[:, 0].tolist()  # Extract the first column values as a list\n", "    \n", "    # Add the first column values to the list\n", "    first_columns.extend(first_column)\n", "\n", "    # add source details\n", "    if file == 'zsample.csv':\n", "        df['source'] = 'PERH_data-BAMS-ENC'\n", "    else:\n", "        df['source'] = \"_\".join(file.split(\".\")[0].split(\"_\")[1:])\n", "\n", "    res_df.append(df[['patient', 'source']])\n", "\n", "    # if 'bam' in df.columns:\n", "    #     res_df.append(df[['patient', 'source', 'bam']])\n", "    \n", "\n", "first_columns = np.unique(first_columns)\n", "\n", "# print('\", \"'.join(first_columns))\n", "print(len(first_columns))\n", "\n", "res_df = pd.concat(res_df)\n", "mask = ~res_df['patient'].duplicated()\n", "res_df = res_df.loc[mask,:]\n", "\n", "res_df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/samples.tsv\", sep='\\t', index=False)\n", "res_df\n"]}, {"cell_type": "code", "execution_count": null, "id": "db433494-2cc2-4b33-bc71-94d58a08b2ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "503f4536-1710-440c-9a82-6025de9bdff4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "aca9b2df-6f4d-483f-93b0-f990d0c453bf", "metadata": {}, "outputs": [], "source": ["\n", "\n", "main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08'\n", "\n", "regex_patterns = ['mutect2.filtered.vcf.gz', 'strelka.variants.vcf.gz', 'freebayes.vcf.gz']\n", "dir_list = ['mutect2_filtered', 'strelka_raw', 'freebayes_raw']\n", "destination_dir_list = [os.path.join(main_dir, 'all_vcfs', x) for x in dir_list]\n", "\n", "\n", "\n", "samples = []\n", "\n", "for i, regex_pattern in enumerate(regex_patterns):\n", "    # Define the source directory\n", "    dir_list = ['results-nf_decrypted-data-DNA-ENC', 'results-nf_PERH2-BAMS-ENC', 'results-nf_PERH_data-BAMS-ENC', 'results-zsample', 'results-X201612680']\n", "    source_dirs = [os.path.join(main_dir, x) for x in dir_list]\n", "\n", "    if not os.path.exists(destination_dir_list[i]):\n", "        os.makedirs(destination_dir_list[i])\n", "    \n", "    # Function to create symbolic links for files matching the regex pattern\n", "    def create_symbolic_links(source_dir):\n", "        # Search for files matching the regex pattern in the source directory\n", "        files = glob.glob(os.path.join(source_dir, '**', '**', '*'), recursive=True)\n", "        matching_files = [file for file in files if file.endswith(regex_pattern)]\n", "        matching_files.extend([file for file in files if file.endswith(regex_pattern + \".tbi\")])\n", "\n", "        samples.extend(pd.Series(matching_files).str.split(\"/\").str[-2].str.split(\"-\").str[0])\n", "        \n", "        # Create symbolic links for matching files in the destination directory\n", "        for file_path in matching_files:\n", "            file_name = os.path.basename(file_path)\n", "            destination_link = os.path.join(destination_dir_list[i], file_name)\n", "            if not os.path.exists(destination_link):\n", "                os.symlink(file_path, destination_link)\n", "    \n", "    # \n", "    for source_dir in source_dirs:\n", "        create_symbolic_links(source_dir)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "af996b1a-651c-4437-bd45-c757c5467eff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/plain": ["'X201612680'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["print(len(np.unique(samples)))\n", "'\", \"'.join(np.unique(samples))"]}, {"cell_type": "code", "execution_count": null, "id": "208a6621-ffd4-44f4-a5fa-540f07a32e06", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07a91e36-33c4-4afb-ae99-0e699a91d2b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b08118d4-632c-439e-b906-345d61853252", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d70b7642-b6bc-4294-9b7c-831f5cc4aed8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fefbb492-d0f9-40c8-96a3-8b0ebefd6ee0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f67a274-42d0-4daa-8d2b-16174d7a4b87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "acef4210-1885-4aa9-876e-21ae274feb79", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}