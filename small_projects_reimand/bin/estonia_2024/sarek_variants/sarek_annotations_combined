#!/bin/bash
#$ -P reimandlab
#$ -N estonia_annotations
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/estonia_2024_new/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/estonia_2024_new/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
conda activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/annotations

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'
# export NXF_HOME=/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/annotations/.nextflow


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-sarek_3.5.1/3_5_1 \
--step annotate \
--input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek_variants/sarek_combined_annotations.csv \
--outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/annotations \
-profile singularity \
-c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek_variants/nextflow.config \
--tools 'snpeff,vep,merge' \
--snpeff_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/snpeff_cache/ \
--vep_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/vep_cache/ \
--igenomes_base /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/ \
-resume


