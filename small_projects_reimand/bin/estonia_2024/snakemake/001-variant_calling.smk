# Alec Bahcheli - <EMAIL>

##########################################
# SNVs
##########################################

#####################
# sorting and indexing
#####################

# sort alignment
rule sort_bam_wgs:
    input:
        processed_bam = RES_DIR + "/all_variant_calling/bam_files/{sample}.bam"
        
    output:
        sorted_bam = RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort -o {output.sorted_bam} {input.processed_bam}"

# index alignment
rule index_bam_wgs:
    input:
        sorted_bam = RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam"
        
    output:
        sorted_index = RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam.bai"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools index {input.sorted_bam}"


#####################
# Mutect2
#####################

rule Mutect2:
    input:
        RES_DIR + "/all_variant_calling/variant_calling/{sample}/mutect2/null.txt",
        RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam.bai",
        genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/tmp/tmp/hg38.fa",
        
        sorted_bam = RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/all_variant_calling/variant_calling/{sample}/mutect2_{chromosome}.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '30g',
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/all_variant_calling/variant_calling/{sample}/mutect2"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '35G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.sorted_bam} --output {output.output_vcf_gz} --tmp-dir {params.sample_tmp_dir} --intervals {params.chromosome}"


rule filter_Mutect2:
    input:
        genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/tmp/tmp/hg38.fa",

        input_vcf_gz = RES_DIR + "/all_variant_calling/variant_calling/{sample}/mutect2_{chromosome}.vcf.gz"

    output:
        output_vcf_gz = RES_DIR + "/all_variant_calling/variant_calling/{sample}/filtered_mutect2_{chromosome}.vcf.gz"

    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '30g',
        sample_tmp_dir = RES_DIR + "/all_variant_calling/variant_calling/{sample}_wgs_seq/mutect2/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '35G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' FilterMutectCalls --reference {input.genome_fasta} --variant {input.input_vcf_gz} --output {output.output_vcf_gz} --tmp-dir {params.sample_tmp_dir}"


rule merge_mutect2_vcfs:
    input:
        expanded_files = expand("{res_dir}/all_variant_calling/variant_calling/{sample}/mutect2_{chromosome}.vcf.gz", res_dir = RES_DIR, sample = samples, chromosome = chromosome_list)
        
    output:
        output_vcf = RES_DIR + "/all_variant_calling/variant_calling/{sample}/{sample}-mutect2.filtered.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.expanded_files} | gzip > {output.output_vcf}"




#####################
# Strelka
#####################

rule init_Strelka:
    input:
        genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/tmp/tmp/hg38.fa",

        tumor_bam = RES_DIR + "/all_variant_calling/sorted_bam_files/{sample}-sorted.bam",

    output:
        RES_DIR + "/all_variant_calling/variant_calling/{sample}/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/all_variant_calling/variant_calling/{sample}"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --tumorBam {input.tumor_bam} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"


rule Strelka:
    input:
        run_file = RES_DIR + "/all_variant_calling/variant_calling/{sample}/runWorkflow.py"
        
    output:
        RES_DIR + "/all_variant_calling/variant_calling/{sample}/results/variants/somatic.snvs.vcf.gz",
        RES_DIR + "/all_variant_calling/variant_calling/{sample}/results/variants/somatic.indels.vcf.gz"
        
    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'

    shell:
        "{input.run_file} -m local -j {resources.threads}"


rule merge_strelka_vcfs:
    input:
        snvs_file = RES_DIR + "/all_variant_calling/variant_calling/{sample}/results/variants/somatic.snvs.vcf.gz",
        indels_file = RES_DIR + "/all_variant_calling/variant_calling/{sample}/results/variants/somatic.indels.vcf.gz"

    output:
        output_vcf = RES_DIR + "/all_variant_calling/variant_calling/{sample}/{sample}-tumor.strelka.filtered.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.snvs_file} {input.indels_file} | gzip > {output.output_vcf}"







