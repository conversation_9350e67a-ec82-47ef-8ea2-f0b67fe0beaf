# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# variant processing
#####################

# gzip vcf
rule bgzip_vcf:
    input:
        input_vcf = RES_DIR + "/all_vcfs/{variant_dir}/{variant_file}.vcf"

    output:
        vcf_index = RES_DIR + "/all_vcfs/{variant_dir}/{variant_file}.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip -k {input.input_vcf}"

# index vcfs
rule index_vcf:
    input:
        input_vcf = RES_DIR + "/all_vcfs/{variant_dir}/{variant_file}"

    output:
        vcf_index = RES_DIR + "/all_vcfs/{variant_dir}/{variant_file}.tbi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools index -t {input.input_vcf}"""



#####################
# variant filtering
#####################

# filter freebayes
rule filter_freebayes:
    input:
        input_vcf = RES_DIR + "/all_vcfs/freebayes_raw/{sample}-tumor.freebayes.vcf.gz"

    output:
        filtered_vcf = RES_DIR + "/all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    params:
        minimum_quality_score = 80

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'QUAL > {params.minimum_quality_score}' {input.input_vcf} -o {output.filtered_vcf}"


# filter strelka 
rule filter_strelka:
    input:
        input_vcf = RES_DIR + "/all_vcfs/strelka_raw/{sample}-tumor.strelka.variants.vcf.gz"

    output:
        filtered_vcf = RES_DIR + "/all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'FILTER="PASS"' {input.input_vcf} -o {output.filtered_vcf}"""

# filter mutect2 
rule filter_mutect2:
    input:
        input_vcf = RES_DIR + "/all_vcfs/mutect2_raw/{sample}-tumor.mutect2.filtered.vcf.gz"

    output:
        filtered_vcf = RES_DIR + "/all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'FILTER="PASS"' {input.input_vcf} -o {output.filtered_vcf}"""


# create a unique directory for each sample
rule create_res_dir:
    input:
        input_vcf = RES_DIR + "/all_vcfs/strelka_raw/{sample}-tumor.strelka.variants.vcf.gz"

    output:
        null_file = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/null.txt"

    params:
        output_dir = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "mkdir -p {params.output_dir} && touch {output.null_file}"


# normalize variant calls
rule normalize_vcfs:
    input:
        genome_fasta = RES_DIR + "/all_vcfs/Homo_sapiens_assembly38.fasta",

        input_vcf_index = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.vcf.gz.tbi",
        input_vcf = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.vcf.gz"

    output:
        normalized_vcf = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools norm -O z -f {input.genome_fasta} -o {output.normalized_vcf} {input.input_vcf}"


# subset to exomes
rule subset_to_exomes:
    input:
        illumina_exome_bed_file = RES_DIR + '/all_vcfs/Illumina_Exome_TargetedRegions_v1.2.hg38.bed',

        input_vcf_index = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf.gz.tbi",
        input_vcf = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf.gz"

    output:
        exome_vcf = RES_DIR + "/all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.exome.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -R {input.illumina_exome_bed_file} -o {output.exome_vcf} {input.input_vcf} "



#####################
# merge VCFs
#####################

# obtain consensus variants
rule variant_consensus:
    input:
        RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/null.txt",

        freebayes_vcf_index = RES_DIR + "/all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.normalized.exome.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.normalized.exome.vcf.gz.tbi",
        mutect2_vcf_index = RES_DIR + "/all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.normalized.exome.vcf.gz.tbi",


        freebayes_vcf = RES_DIR + "/all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.normalized.exome.vcf.gz",
        strelka_vcf = RES_DIR + "/all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.normalized.exome.vcf.gz",
        mutect2_vcf = RES_DIR + "/all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.normalized.exome.vcf.gz"

    output:
        RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf.gz",
        RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz",
        RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz"

    params:
        outdir = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools isec -O z -n +2 -p {params.outdir} {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"""


# # rename freebayes samples
# rule rename_freebayes_samples:
#     input:
#         freebayes_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz"

#     output:
#         renamed_vcf = temp(RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0003.vcf.gz")

#     params:
#         sample = "{sample}",
#         tumor = "{tumor}"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "zcat {input.freebayes_vcf} | sed 's/^#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\tNORMAL\tTUMOR/#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-blood\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-{params.tumor}/' | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip > {output.renamed_vcf}"



# # normalize multi-allelic vcfs
# rule normalize_vcfs_multiallelic:
#     input:
#         genome_fasta = RES_DIR + "/all_vcfs/Homo_sapiens_assembly38.fasta",

#         input_vcf_index = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/{file}.vcf.gz.tbi",
#         input_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/{file}.vcf.gz"

#     output:
#         normalized_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/normalized_{file}.vcf.gz"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '5G'
        
#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools norm -f {input.genome_fasta} -o {output.normalized_vcf} -m - {input.input_vcf}"



# # rename freebayes samples
# rule rename_freebayes_samples:
#     input:
#         freebayes_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz",

#     output:
#         renamed_vcf = (RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0003.vcf.gz")

#     params:
#         sample = "{sample}",
#         tumor = "{tumor}"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "zcat {input.freebayes_vcf} | sed 's/^#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\tNORMAL\tTUMOR/#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-blood\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-{params.tumor}/' | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip > {output.renamed_vcf}"

# # rename strelka samples
# rule rename_strelka_samples:
#     input:
#         freebayes_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz",

#     output:
#         renamed_vcf = (RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0004.vcf.gz")

#     params:
#         sample = "{sample}",
#         tumor = "{tumor}"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "zcat {input.freebayes_vcf} | sed 's/^#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\tNORMAL\tTUMOR/#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-blood\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-{params.tumor}/' | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip > {output.renamed_vcf}"



# merge vcf
rule merge_vcfs:
    input:
        RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/null.txt",

        mutect2_vcf_index = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz.tbi",
        freebayes_vcf_index = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz.tbi",

        mutect2_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf.gz",
        strelka_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz",
        freebayes_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz"

    output:
        merged_vcf = RES_DIR + "/all_vcfs/results/{sample}-tumor_combined.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools concat -o {output.merged_vcf} --allow-overlaps -O z {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"


# filter vcf to remove repeats
rule filter_vcf_to_remove_repeats:
    input:
        merged_vcf = RES_DIR + "/all_vcfs/results/{sample}-tumor_combined.vcf",

        script = BIN_DIR + "/020a-filter_vcf_to_remove_repeats.py"

    output:
        succinct_vcf = RES_DIR + "/all_vcfs/results_succinct/{sample}-tumor_combined.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --merged_vcf {input.merged_vcf} --succinct_vcf {output.succinct_vcf}"











