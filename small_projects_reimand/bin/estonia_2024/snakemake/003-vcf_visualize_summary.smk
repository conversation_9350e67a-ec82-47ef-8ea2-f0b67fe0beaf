# Alec Bahcheli


#####################
# VCF variant summaries
#####################

# bcftools summary
rule variant_stats:
    input:
        input_vcf = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}.vcf"

    output:
        summary_file = RES_DIR + "/all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}-stats.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools stats {input.input_vcf} > {output.summary_file}"



rule summarize_vcf_variants:
    input:
        expand("{res_dir}/all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}-stats.txt", res_dir = RES_DIR, sample = samples, vcf_file = ['0000', '0001', '0002']),

        sample_recurrence_file = FIGURE_DATA_DIR + "/2023_december_12_NR_and_R_patient_data.csv",
        samples_description_file = FIGURE_DATA_DIR + "/samples.tsv",

        script = BIN_DIR + "/001a-vcf_summary.py"

    output:
        figure_data_file = FIGURE_DATA_DIR + "/001-vcf_summary.tsv"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --samples_description_file {input.samples_description_file} --sample_recurrence_file {input.sample_recurrence_file} --figure_data_file {output.figure_data_file} --threads {resources.threads}"



rule vcf_variant_summary_pdf:
    input:
        figure_data_file = FIGURE_DATA_DIR + "/001-vcf_summary.tsv",

        r_script = BIN_DIR + "/001b-vcf_summary.R"

    output:
        figure_file = FIGURE_DIR + "/001-vcf_summary.pdf"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



rule unique_vcf_variant_summary:
    input:
        figure_data_file = FIGURE_DATA_DIR + "/002-annotated_variants_vcf_0.01.tsv",

        script = BIN_DIR + "/001c-unique_vcf_summary.py"

    output:
        figure_stats_file = FIGURE_DATA_DIR + "/001-unique_vcf_summary.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{PYTHON} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {output.figure_stats_file}"


rule unique_vcf_variant_summary_pdf:
    input:
        figure_data_file = FIGURE_DATA_DIR + "/001-unique_vcf_summary.tsv",

        r_script = BIN_DIR + "/001d-unique_vcf_summary.R"

    output:
        figure_file = FIGURE_DIR + "/001-unique_vcf_summary.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




#####################
# annotated variant summaries
#####################

rule variant_consequences_summary:
    input:
        expand("{res_dir}/all_vcfs/annotation/{sample}-tumor/{sample}-tumor_combined_VEP.ann.vcf.gz", res_dir = RES_DIR, sample = samples),

        sample_recurrence_file = FIGURE_DATA_DIR + "/2023_december_12_NR_and_R_patient_data.csv",
        samples_description_file = FIGURE_DATA_DIR + "/samples.tsv",

        script = BIN_DIR + "/002a-vcf_summary.py"

    output:
        annotated_variants_file = FIGURE_DATA_DIR + "/002-annotated_variants_vcf_{cutoff}.tsv",

        figure_data_file = FIGURE_DATA_DIR + "/002-annotated_summary_{cutoff}.tsv"

    params:
        cutoff = '{cutoff}',
        results_dir = RES_DIR + "/all_vcfs/annotation"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {params.results_dir} --samples_description_file {input.samples_description_file} --sample_recurrence_file {input.sample_recurrence_file} --annotated_variants_file {output.annotated_variants_file} --figure_data_file {output.figure_data_file} --cutoff {params.cutoff} --threads {resources.threads}"



rule variant_consequences_summary_pdfs:
    input:
        figure_data_file = RES_DIR + "/_figure_data/002-annotated_summary_{cutoff}.tsv",

        r_script = BIN_DIR + "/002b-vcf_summary.R"

    output:
        figure_file = RES_DIR + "/_figures/002-annotated_summary_{cutoff}.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



rule protein_coding_impacts:
    input:
        annotated_variant_file = RES_DIR + "/_figure_data/002-annotated_variants_vcf_0.01.tsv",
        cgc_file = DATA_DIR + "/cgc_v98_29062023.tsv",

        script = BIN_DIR + "/002c-classify_protein_impact.py"

    output:
        results_file = RES_DIR + "/_figure_data/002-details_classify_protein_impact.tsv",
        cgc_only_results_file = RES_DIR + "/_figure_data/002-cgc_only_protein_impact.tsv",
        figure_data_file = RES_DIR + "/_figure_data/002-summary_classify_protein_impact.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{PYTHON} {input.script} --annotated_variant_file {input.annotated_variant_file} --cgc_file {input.cgc_file} --results_file {output.results_file} --cgc_only_results_file {output.cgc_only_results_file} --figure_data_file {output.figure_data_file}"



rule visualize_protein_coding_impacts:
    input:
        figure_data_file = RES_DIR + "/_figure_data/002-summary_classify_protein_impact.tsv",

        r_script = BIN_DIR + "/002d-classify_protein_impact.R"

    output:
        figure_file = RES_DIR + "/_figures/002-summary_classify_protein_impact.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



rule visualize_cgc_impact:
    input:
        cgc_only_results_file = RES_DIR + "/_figure_data/002-cgc_only_protein_impact.tsv",

        script = BIN_DIR + "/002e-summarize_cgc_impact.py",
        r_script = BIN_DIR + "/002f-summarize_cgc_impact.R"

    output:
        figure_stats_file = RES_DIR + "/_figure_data/002-cgc_only_protein_impact_stats.tsv",
        figure_file = RES_DIR + "/_figures/002-summarize_cgc_impact.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --figure_data_file {input.cgc_only_results_file} --r_script {input.r_script} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"


rule oncoprint_cgc_genes:
    input:
        figure_data_file = RES_DIR + "/_figure_data/002-cgc_only_protein_impact.tsv",

        r_script = BIN_DIR + "/002g-oncoprint_cgc_genes.R"

    output:
        figure_file = RES_DIR + "/_figures/002-oncoprint_cgc_genes.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




