# Alec <PERSON>


#####################
# process the annotated VCFs
#####################

# process vcfs into a single df
rule subset_and_expand_vcfs:
    input:
        RES_DIR + "/all_vcfs/all_annotations/{sample}-tumor_combined_snpEff_VEP.ann.vcf.gz.tbi",
        annotated_vcf_consensus_file = RES_DIR + "/all_vcfs/all_annotations/{sample}-tumor_combined_snpEff_VEP.ann.vcf.gz",

        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",

        script = BIN_DIR + "/010a-process_and_separate_columns_vcfs.py"

    output:
        expanded_data_file = RES_DIR + "/all_vcfs/finalized_annotated/{sample}-tumor_snpEFF_VEP.tsv",
        filtered_vcf_file = RES_DIR + "/all_vcfs/finalized_annotated_vcfs/{sample}-tumor_snpEFF_VEP.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --annotated_vcf_consensus_file {input.annotated_vcf_consensus_file} --cgc_file {input.cgc_file} --expanded_data_file {output.expanded_data_file} --filtered_vcf_file {output.filtered_vcf_file}"

# process vcfs into a single df
rule subset_raw_vcfs:
    input:
        RES_DIR + "/all_vcfs/results/{sample}-tumor_combined.vcf.gz.tbi",
        vcf_consensus_file = RES_DIR + "/all_vcfs/results/{sample}-tumor_combined.vcf.gz",

        script = BIN_DIR + "/011a-subset_raw_vcfs.py"

    output:
        filtered_vcf_file = RES_DIR + "/all_vcfs/finalized_unannotated_vcfs/{sample}-tumor.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --vcf_consensus_file {input.vcf_consensus_file} --filtered_vcf_file {output.filtered_vcf_file}"






