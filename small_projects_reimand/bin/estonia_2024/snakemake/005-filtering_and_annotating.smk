# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# variant filtering
#####################

# filter vcf to remove repeats
rule remove_high_af_mutations:
    input:
        annotated_vcf = RES_DIR + "/all_vcfs/all_annotations/{sample}-tumor_combined_VEP.ann.vcf.gz",

        script = BIN_DIR + "/021a-remove_high_af_mutations.py"

    output:
        filtered_vcf = RES_DIR + "/all_vcfs/filtered_annotations_af/{sample}-filtered_VEP.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --annotated_vcf {input.annotated_vcf} --filtered_vcf {output.filtered_vcf}"

rule sigprofiler_signature_assignment:
    input:
        expand("{res_dir}/all_vcfs/filtered_annotations_af/{sample}-filtered_VEP.vcf", res_dir = RES_DIR, sample = samples),

        script = BIN_DIR + "/022a-sigprofiler_signature_assignment.py"

    output:
        signature_finished_file = RES_DIR + "/analysis_sarek/sigprofiler/JOB_METADATA_SPA.txt"

    params:
        sigprofiler_python = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/sigprofiler/bin/python",

        sigprofiler_input_dir = RES_DIR + "/all_vcfs/filtered_annotations_af",
        sigprofiler_res_dir = RES_DIR + "/all_vcfs/sigprofiler"

    resources:
        threads = 1,
        queue = "u20build",
        jobtime = '0:4:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{params.sigprofiler_python} {input.script} --input_dir {params.sigprofiler_input_dir} --sigprofiler_res_dir {params.sigprofiler_res_dir}"







