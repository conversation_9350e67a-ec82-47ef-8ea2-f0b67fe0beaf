# Alec <PERSON>cheli - <EMAIL>

#####################
# indel comparison
#####################

# compare indel counts between two result directories
rule compare_indels:
    input:
        script = BIN_DIR + "/006a-compare_indels.py"

    output:
        comparison_tsv = RES_DIR + "/indel_comparison/indel_counts_comparison.tsv"

    params:
        new_results_dir = RES_DIR + "/all_vcfs/results_succinct/",
        old_results_dir = RES_DIR + "/all_vcfs/results_succinct_early_2025/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --new_results_dir {params.new_results_dir} --old_results_dir {params.old_results_dir} --output_tsv {output.comparison_tsv}"


# visualize indel comparison results
rule visualize_indel_comparison:
    input:
        comparison_tsv = RES_DIR + "/indel_comparison/indel_counts_comparison.tsv",
        script = BIN_DIR + "/006b-visualize_indel_comparison.R"

    output:
        comparison_plot = RES_DIR + "/indel_comparison/indel_comparison_plots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -i {input.comparison_tsv} -o {output.comparison_plot}"




