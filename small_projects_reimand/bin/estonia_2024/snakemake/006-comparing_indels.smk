# Alec Ba<PERSON>cheli - <EMAIL>

#####################
# indel comparison
#####################

# compare indel counts between two result directories
rule compare_indels:
    input:
        expand("{res_dir}/all_vcfs/results_succinct/{sample}-tumor_combined.vcf", res_dir = RES_DIR, sample = samples),

        script = BIN_DIR + "/006a-compare_indels.py"

    output:
        comparison_tsv = RES_DIR + "/indel_snv_comparisons/indel_counts_comparison.tsv"

    params:
        new_results_dir = RES_DIR + "/all_vcfs/results_succinct/",
        old_results_dir = RES_DIR + "/all_vcfs/results_succinct_early_2025/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --new_results_dir {params.new_results_dir} --old_results_dir {params.old_results_dir} --output_tsv {output.comparison_tsv}"


# visualize indel comparison results
rule visualize_indel_comparison:
    input:
        comparison_tsv = RES_DIR + "/indel_snv_comparisons/indel_counts_comparison.tsv",
        script = BIN_DIR + "/006b-visualize_indel_comparison.R"

    output:
        comparison_plot = RES_DIR + "/indel_snv_comparisons/indel_comparison_plots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -i {input.comparison_tsv} -o {output.comparison_plot}"


# create paired barplots for indel counts (old vs new)
rule paired_indel_barplots:
    input:
        comparison_tsv = RES_DIR + "/indel_snv_comparisons/indel_counts_comparison.tsv",
        script = BIN_DIR + "/006g-paired_indel_barplots.R"

    output:
        paired_barplots = RES_DIR + "/indel_snv_comparisons/paired_indel_barplots.pdf"

    params:
        samples_per_page = 50

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -input_tsv {input.comparison_tsv} -output_plot {output.paired_barplots} -samples_per_page {params.samples_per_page}"



#####################
# effects of new indel variants
#####################

# analyze protein effects of new indels using VEP annotations
rule analyze_new_indel_effects:
    input:
        expand("{res_dir}/all_vcfs/filtered_annotations_2025_new/{sample}-filtered_VEP.vcf", res_dir = RES_DIR, sample = samples),

        script = BIN_DIR + "/007a-analyze_new_indel_effects.py"

    output:
        effects_tsv = RES_DIR + "/indel_snv_comparisons/new_indel_effects.tsv"

    params:
        new_vcf_dir = RES_DIR + "/all_vcfs/filtered_annotations_2025_new/",
        old_vcf_dir = RES_DIR + "/all_vcfs/filtered_annotations_2025_old/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '3:0:0:0',
        individual_core_memory = '15G'

    shell:
        "{PYTHON} {input.script} --new_vcf_dir {params.new_vcf_dir} --old_vcf_dir {params.old_vcf_dir} --output_tsv {output.effects_tsv}"


# summarize new indel effects
rule summarize_indel_effects:
    input:
        effects_tsv = RES_DIR + "/indel_snv_comparisons/new_indel_effects.tsv",
        script = BIN_DIR + "/007b-summarize_indel_effects.py"

    output:
        simple_tsv = RES_DIR + "/indel_snv_comparisons/indel_effects_simple.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{PYTHON} {input.script} --input_tsv {input.effects_tsv} --output_simple {output.simple_tsv}"


# visualize new indel effects
rule visualize_indel_effects:
    input:
        simple_tsv = RES_DIR + "/indel_snv_comparisons/indel_effects_simple.tsv",
        script = BIN_DIR + "/007c-visualize_indel_effects.R"

    output:
        effects_plot = RES_DIR + "/indel_snv_comparisons/indel_effects_plots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -i {input.simple_tsv} -o {output.effects_plot}"


#####################
# SNV comparison
#####################

# compare SNV counts between two result directories
rule compare_snvs:
    input:
        expand("{res_dir}/all_vcfs/results_succinct/{sample}-tumor_combined.vcf", res_dir = RES_DIR, sample = samples),

        script = BIN_DIR + "/006c-compare_snvs.py"

    output:
        comparison_tsv = RES_DIR + "/indel_snv_comparisons/snv_counts_comparison.tsv"

    params:
        new_results_dir = RES_DIR + "/all_vcfs/results_succinct/",
        old_results_dir = RES_DIR + "/all_vcfs/results_succinct_early_2025/"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '7G'

    shell:
        "{PYTHON} {input.script} --new_results_dir {params.new_results_dir} --old_results_dir {params.old_results_dir} --output_tsv {output.comparison_tsv} --threads {resources.threads}"


# visualize SNV comparison results
rule visualize_snv_comparison:
    input:
        comparison_tsv = RES_DIR + "/indel_snv_comparisons/snv_counts_comparison.tsv",
        script = BIN_DIR + "/006d-visualize_snv_comparison.R"

    output:
        comparison_plot = RES_DIR + "/indel_snv_comparisons/snv_comparison_plots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -i {input.comparison_tsv} -o {output.comparison_plot}"


#####################
# variant ratio analysis
#####################

# calculate variant ratios (indels/SNVs, SNVs/indels, indels/total)
rule calculate_variant_ratios:
    input:
        indel_comparison = RES_DIR + "/indel_snv_comparisons/indel_counts_comparison.tsv",
        snv_comparison = RES_DIR + "/indel_snv_comparisons/snv_counts_comparison.tsv",
        script = BIN_DIR + "/006e-calculate_variant_ratios.py"

    output:
        ratios_tsv = RES_DIR + "/indel_snv_comparisons/variant_ratios.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{PYTHON} {input.script} --indel_comparison {input.indel_comparison} --snv_comparison {input.snv_comparison} --output_tsv {output.ratios_tsv}"


# visualize variant ratio comparisons
rule visualize_variant_ratios:
    input:
        ratios_tsv = RES_DIR + "/indel_snv_comparisons/variant_ratios.tsv",
        script = BIN_DIR + "/006f-visualize_variant_ratios.R"

    output:
        ratios_plot = RES_DIR + "/indel_snv_comparisons/variant_ratios_plots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{RSCRIPT} {input.script} -i {input.ratios_tsv} -o {output.ratios_plot}"




