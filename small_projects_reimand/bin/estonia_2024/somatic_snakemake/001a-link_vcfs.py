# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def make_other_dirs(other_dir_list):
    for other_dir in other_dir_list:
        if not os.path.exists(os.path.join(results_dir, other_dir)):
            os.makedirs(os.path.join(results_dir, other_dir))


def create_samples_tsv(sarek_csv_files_list):
    # Initialize an empty list to store the first columns
    first_columns = []

    res_df = []

    # Loop through each file
    for file in sarek_csv_files_list:
        # Load the DataFrame from the file
        df = pd.read_csv(file)
        
        # Assuming the first column name is 'column_name'
        first_column = df.iloc[:, 0].tolist()  # Extract the first column values as a list
        
        # Add the first column values to the list
        first_columns.extend(first_column)

        df['source'] = "_".join(file.split(".")[0].split("_")[1:])

        res_df.append(df[['patient', 'source']])

    first_columns = np.unique(first_columns)

    res_df = pd.concat(res_df)
    mask = ~res_df['patient'].duplicated()
    res_df = res_df.loc[mask,:]

    return res_df


def link_vcfs(files_suffixes, dir_list, main_dir):
    destination_dir_list = [os.path.join(main_dir, x) for x in dir_list]

    for i, files_suffix in enumerate(files_suffixes):
        # make the destination dirs
        if not os.path.exists(destination_dir_list[i]):
            os.makedirs(destination_dir_list[i])
        
        # Function to create symbolic links for files matching the regex pattern
        def create_symbolic_links(source_dir):
            # Search for files matching the regex pattern in the source directory
            files = glob.glob(os.path.join(source_dir, 'all_vcfs', '**', '*tumor_vs_*', '*'), recursive=True)
            matching_files = [file for file in files if file.endswith(files_suffix)]
            matching_files.extend([file for file in files if file.endswith(files_suffix + ".tbi")])

            # samples.extend(pd.Series(matching_files).str.split("/").str[-2].str.split("-").str[0])
            
            # Create symbolic links for matching files in the destination directory
            for file_path in matching_files:
                file_name = os.path.basename(file_path)
                file_prefix = file_name.split(".")[0].split("_")[0]
                file_suffix = ".".join(file_name.split(".")[1:])

                file_name = file_prefix + "." + file_suffix
                print(file_name)
                
                destination_link = os.path.join(destination_dir_list[i], file_name)
                if not os.path.exists(destination_link):
                    os.symlink(file_path, destination_link)
        
        create_symbolic_links(main_dir)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # make other directories
    make_other_dirs(other_dir_list)

    # link vcfs
    link_vcfs(files_suffixes, dir_list, results_dir)

    # create samples df
    res_df = create_samples_tsv(sarek_csv_files_list)

    # save to file
    res_df.to_csv(samples_description_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # list of sarek reference files
    sarek_csv_files_list = ['/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek/sarek_decrypted-data-DNA-ENC.csv']

    # vcf files
    files_suffixes = ['mutect2.filtered.vcf.gz', 'strelka.variants.vcf.gz', 'freebayes.vcf.gz']

    # destination dirs corresponding to the vcf files
    dir_list = ['mutect2_filtered', 'strelka_raw', 'freebayes_raw']

    # other directories to make
    other_dir_list = ['annotations', 'consensus_vcfs', 'freebayes_filtered', 'freebayes_raw', 'mutect2_filtered', 'results', 'strelka_filtered', 'strelka_raw']


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "samples_description_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--samples_description_file"):
            samples_description_file = str(arg)

    main()


