# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def load_df(file):
    # Read the lines starting with "SN" into a list
    with open(file, 'r') as f:
        sn_lines = [line.strip() for line in f if line.startswith('SN')]

    # Create a DataFrame from the filtered lines
    df = pd.DataFrame([line.split('\t')[2:] for line in sn_lines], columns=['key', 'value'])

    # clean up
    df['key'] = df['key'].str.lstrip("number of ").str.rstrip(":")

    # add patient and sample name
    df['patient'] = file.split('/')[-2].split("-")[0]
    df['variant_caller'] = vcf_dict[file.split('/')[-1].split(".")[0].strip("-stats")]

    return df



def load_and_process_dfs(file_list, samples_description_file, sample_recurrence_file, threads):
    # multi-thread calculate p-values and fc
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(load_df, file_list))

    # collect converted dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    # add the source of the sample
    samples_df = pd.read_csv(samples_description_file, sep='\t')
    
    # subset to common samples and merge
    res_df['patient'] = res_df['patient'].astype("<U64")
    samples_df['patient'] = samples_df['patient'].astype("<U64")
    res_df = res_df.merge(samples_df, on='patient', how='left')

    # add recurrence details
    recurrence_df = pd.read_csv(sample_recurrence_file)
    recurrence_df.columns = recurrence_df.columns.str.replace(" ", "_")
    recurrence_df['samples'] = recurrence_df['Samples'].str.split("_").str[0].str.lstrip("0")

    # common samples
    common_samples = recurrence_df['samples'][np.isin(recurrence_df['samples'].astype('<U64'), res_df['patient'].astype('<U64'))]

    recurrence_df = recurrence_df[recurrence_df['samples'].isin(common_samples)]

    # add recurrence details
    res_df = res_df.merge(recurrence_df, left_on='patient', right_on='samples', how='left')

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    file_regex = f"{results_dir}/consensus_vcfs/*/*stats.txt"
    files = glob.glob(file_regex)

    # process and combine all dfs
    res_df = load_and_process_dfs(files, samples_description_file, sample_recurrence_file, threads)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    vcf_dict = {'0000':'mutect2', '0001':'strelka2', '0002':'freebayes'}

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "samples_description_file=", "sample_recurrence_file=", "r_script=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--samples_description_file"):
            samples_description_file = str(arg)
        if opt in ("--sample_recurrence_file"):
            sample_recurrence_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


