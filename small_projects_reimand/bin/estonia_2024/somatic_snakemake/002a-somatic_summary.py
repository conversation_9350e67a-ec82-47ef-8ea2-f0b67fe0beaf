# Alec <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''

def load_dfs(somatic_data_file, tumor_data_file):
    # load dfs
    somatic_df = pd.read_csv(somatic_data_file, sep='\t')
    tumor_df = pd.read_csv(tumor_data_file, sep='\t')

    somatic_df['patient'] = somatic_df['patient'].astype('<U64')
    tumor_df['patient'] = tumor_df['patient'].astype('<U64')
    
    # subset to common patients
    common_patients = np.intersect1d(somatic_df['patient'].unique(), tumor_df['patient'].unique())
    somatic_df = somatic_df[somatic_df['patient'].isin(common_patients)]
    tumor_df = tumor_df[tumor_df['patient'].isin(common_patients)]
    
    # subset to SNPs and indels
    somatic_df = somatic_df[somatic_df['key'].isin(['SNPs', 'indels'])]
    tumor_df = tumor_df[tumor_df['key'].isin(['SNPs', 'indels'])]

    # subset to relavent columns
    columns_oi = ['key', 'value', 'patient', 'variant_caller']
    somatic_df = somatic_df[columns_oi]
    tumor_df = tumor_df[columns_oi]

    # subtract somatic_df value from tumor_df value for each patient
    key_cols = ['patient', 'key', 'variant_caller']
    res_df = pd.merge(somatic_df, tumor_df, on=key_cols, suffixes=('_somatic', '_tumor'))
    # print(res_df)
    res_df['value'] = res_df['value_tumor'] - res_df['value_somatic']
    res_df = res_df.drop(columns=['value_somatic', 'value_tumor'])

    # add details 
    res_df['type'] = 'germline'
    somatic_df['type'] = 'somatic'

    # append somatic_df to res_df
    res_df = pd.concat([res_df, somatic_df])

    # add str for R
    res_df['patient'] = "X" + res_df['patient']

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load files and subset to patients of interest
    res_df = load_dfs(somatic_data_file, tumor_data_file)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    vcf_dict = {'0000':'mutect2', '0001':'strelka2', '0002':'freebayes'}

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["somatic_data_file=", "tumor_data_file=", "r_script=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--somatic_data_file"):
            somatic_data_file = str(arg)
        if opt in ("--tumor_data_file"):
            tumor_data_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


