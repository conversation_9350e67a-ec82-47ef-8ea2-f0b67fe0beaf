# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





barplot = function(variant_type, input_df){

print(head(input_df))

# set max
# input_df$count[input_df$count > 100000] = 100000
input_df$patient = fct_reorder(as.factor(input_df$patient), input_df$value, sum, .desc=TRUE)

# plot raw expression
p = ggplot(input_df, aes(x = patient, y = value, fill = type)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered)') +

facet_grid(. ~ variant_caller)


print(p)

return()
}






pdf(opt$figure_file, width = 20)

# load df
df = read.csv(opt$figure_data_file, sep='\t')
df$type = fct_reorder(as.factor(df$type), df$value, sum, .desc=FALSE)

print(head(df))


for (key in unique(df$key)){

main_df = df[df$key == key,]

# create plot
barplot(key, main_df)

}




dev.off()


print(opt$figure_file)




