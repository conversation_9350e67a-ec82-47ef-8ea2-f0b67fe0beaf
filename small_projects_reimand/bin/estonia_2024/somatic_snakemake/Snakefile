# Alec Bahcheli

# run version (typically date)
VERSION='estonia_2024/2024_05_08'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", VERSION, "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", VERSION, "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'



# dir_list = ['mutect2_filtered', 'strelka_raw', 'freebayes_raw']
# software = ['mutect2', 'strelka', 'freebayes']


samples = ['1008', '1009', '1058', '106', '107', '108', '115', '146', '153', '157', '158', '257', '294', '300', '301', '302', '303', '304', '305', '343', '344', '354', '363', '364', '384', '396', '397', '427', '749', '750', '859', '860', '892', '905', '906', '91', '927', '94', '941']


###################################
# Complete workflow
###################################


# define the objective (make the output files)
rule all:
    input:
        RES_DIR + "/_figures/001-somatic_vcf_summary.pdf",
        tmp = RES_DIR + "/_figures/002-somatic_vs_tumor.pdf"
        # expand("{res_dir}/somatic_all_vcfs/all_vcfs/variant_calling/strelka/combined_strelka/{sample}-tumor_vs_{sample}-normal.strelka.variants.vcf.{suffix}", res_dir = RES_DIR, sample = samples, suffix = ['gz'])

        # expand("{res_dir}/somatic_all_vcfs/results/{sample}-tumor_combined.vcf.gz.tbi", res_dir = RES_DIR, sample = samples)



# create main project directories
rule main_directories:        
    output:
        data = DATA_DIR + "/null.txt",
        raw_data = RAW_DATA_DIR + "/null.txt",
        ref_data = REF_DATA_DIR + "/null.txt",
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR} {DATA_DIR} {RAW_DATA_DIR} {REF_DATA_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures} {output.data} {output.raw_data} {output.ref_data}")




#####################
# gprofiler analysis 
#####################

include: "snakemake/002-variant_analysis.smk"


