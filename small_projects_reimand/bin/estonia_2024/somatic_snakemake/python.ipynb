{"cells": [{"cell_type": "code", "execution_count": null, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "try:\n", "    sys.path.insert(1, \"/\".join(os.path.realpath(__file__).split(\"/\")[:-2]))\n", "except:\n", "    sys.path.insert(1, \"/\".join(os.getcwd().split(\"/\")[:-1]))\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n"]}, {"cell_type": "code", "execution_count": null, "id": "5a4d9ff9-b123-4980-a3d3-8eb41cb5c01e", "metadata": {}, "outputs": [], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/002-annotated_variants_vcf.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t')\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "832ddb36-ebae-4d07-b855-778d6e5f7e1a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae3d2b15-0eb3-4528-acd8-2f6020ecf603", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "82a3e06a-bef7-4756-a932-c8556633af0a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient</th>\n", "      <th>sample</th>\n", "      <th>vcf</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1008</td>\n", "      <td>1008-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1009</td>\n", "      <td>1009-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1058</td>\n", "      <td>1058-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>106</td>\n", "      <td>106-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>107</td>\n", "      <td>107-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>X201702805</td>\n", "      <td>X201702805-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>X201721754</td>\n", "      <td>X201721754-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>X201800915</td>\n", "      <td>X201800915-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>X201916389</td>\n", "      <td>X201916389-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>ZZZ04911</td>\n", "      <td>ZZZ04911-tumor</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>209 rows × 3 columns</p>\n", "</div>"], "text/plain": ["        patient            sample  \\\n", "0          1008        1008-tumor   \n", "1          1009        1009-tumor   \n", "2          1058        1058-tumor   \n", "3           106         106-tumor   \n", "4           107         107-tumor   \n", "..          ...               ...   \n", "204  X201702805  X201702805-tumor   \n", "205  X201721754  X201721754-tumor   \n", "206  X201800915  X201800915-tumor   \n", "207  X201916389  X201916389-tumor   \n", "208    ZZZ04911    ZZZ04911-tumor   \n", "\n", "                                                   vcf  \n", "0    /.mounts/labs/reimandlab/private/users/abahche...  \n", "1    /.mounts/labs/reimandlab/private/users/abahche...  \n", "2    /.mounts/labs/reimandlab/private/users/abahche...  \n", "3    /.mounts/labs/reimandlab/private/users/abahche...  \n", "4    /.mounts/labs/reimandlab/private/users/abahche...  \n", "..                                                 ...  \n", "204  /.mounts/labs/reimandlab/private/users/abahche...  \n", "205  /.mounts/labs/reimandlab/private/users/abahche...  \n", "206  /.mounts/labs/reimandlab/private/users/abahche...  \n", "207  /.mounts/labs/reimandlab/private/users/abahche...  \n", "208  /.mounts/labs/reimandlab/private/users/abahche...  \n", "\n", "[209 rows x 3 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["samples = [\"1008\", \"1009\", \"1058\", \"106\", \"107\", \"108\", \"115\", \"1284\", \"146\", \"153\", \"157\", \"158\", \"15936\", \"257\", \"294\", \"300\", \"301\", \"302\", \"303\", \"304\", \"305\", \"343\", \"344\", \"354\", \"363\", \"364\", \"384\", \"396\", \"397\", \"410482\", \"410487\", \"415218\", \"427\", \"45090\", \"518400\", \"54969\", \"56185\", \"58004\", \"58669\", \"611715\", \"612509\", \"615164\", \"61549\", \"618466\", \"618925\", \"619189\", \"63242\", \"64224\", \"64647\", \"64815\", \"67639\", \"69393\", \"710223\", \"711005\", \"711305\", \"711441\", \"712599\", \"718976\", \"72017\", \"73310\", \"74306\", \"749\", \"750\", \"79228\", \"79856\", \"805595\", \"811989\", \"816174\", \"817344\", \"818318\", \"859\", \"860\", \"892\", \"901289\", \"901826\", \"904684\", \"905\", \"905344\", \"906\", \"91\", \"916285\", \"918500\", \"927\", \"94\", \"941\", \"IVKH\", \"M1207987\", \"M1308464\", \"M1400519\", \"M1402871\", \"M1502315\", \"M201101474\", \"M201102498\", \"M201109265\", \"M201109514\", \"M201200852\", \"M201201464\", \"M201202994\", \"M201207597\", \"M201208496\", \"M201301448\", \"M201301639\", \"M201302364\", \"M201305657\", \"M201305817\", \"M201305818\", \"M201305986\", \"M201307747\", \"M201308134\", \"M201309141\", \"M201309783\", \"M201400218\", \"M201400880\", \"M201401097\", \"M201401285\", \"M201401950\", \"M201402017\", \"M201402485\", \"M201402870\", \"M201403436\", \"M201403572\", \"M201403672\", \"M201404036\", \"M201404144\", \"M201404940\", \"M201405126\", \"M201405277\", \"M201405596\", \"M201406061\", \"M201406184\", \"M201500596\", \"M201500632\", \"M201501276\", \"M201501320\", \"M201501554\", \"M201501652\", \"M201501653\", \"M201502042_A\", \"M201502042_B\", \"M201503294\", \"M201504055\", \"M201504156\", \"M201504347\", \"M201505317\", \"M201505598\", \"M201505599\", \"M201506208\", \"M201506834\", \"M201507012\", \"M201507466\", \"M201507993\", \"M201508410\", \"M201508988\", \"MZZ0Z755\", \"O201009049\", \"O201012423\", \"O201012924\", \"P1400242\", \"P1403053\", \"P1409332\", \"P201103401\", \"P201103547\", \"P201110327\", \"P201110566\", \"P201209910\", \"P201300967\", \"P201303028\", \"P201303501\", \"P201305237\", \"P201306082\", \"P201306312\", \"P201306634\", \"P201306887\", \"P201309335\", \"P201310059\", \"P201400025\", \"P201402751\", \"P201406539\", \"P201406675\", \"P201409588\", \"P201409797\", \"P201413005\", \"P201500190\", \"P201500292\", \"P201501442\", \"P201504782\", \"P201509880\", \"X201600572\", \"X201602143\", \"X201602525\", \"X201605202\", \"X201605586\", \"X201605838\", \"X201609690\", \"X201610295\", \"X201610732\", \"X201610893_1\", \"X201610893_2\", \"X201614374\", \"X201617428\", \"X201617706\", \"X201700179\", \"X201701399\", \"X201701413\", \"X201702805\", \"X201721754\", \"X201800915\", \"X201916389\", \"ZZZ04911\"]\n", "\n", "dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/all_vcfs/results'\n", "\n", "df = []\n", "\n", "for sample in samples:\n", "    vcf_file = os.path.join(dir, f'{sample}-tumor_combined.vcf.gz')\n", "    df.append([sample, f\"{sample}-tumor\", vcf_file])\n", "\n", "df = pd.DataFrame(df)\n", "df.columns = 'patient,sample,vcf'.split(\",\")\n", "\n", "df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek_variants/sarek_combined_annotations.csv\", index=False)\n", "\n", "df\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ef6515b-131e-462a-9c90-7f0ff091a5f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "daf64afb-089a-49d0-a3fe-ae1f52abc9ae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "2d39875a-ea1e-442a-a3e7-383edd364fdb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["206\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient</th>\n", "      <th>source</th>\n", "      <th>bam</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>M201502042</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>58669</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>615164</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>712599</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>916285</td>\n", "      <td>PERH2-BAMS-ENC_p1</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>P201501442</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>P201509880</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>X201605586</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>X201610295</td>\n", "      <td>PERH_data-BAMS-ENC_p2</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZZZ04911</td>\n", "      <td>PERH_data-BAMS-ENC</td>\n", "      <td>/.mounts/labs/reimandlab/private/generated_raw...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>167 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       patient                 source  \\\n", "0   M201502042      PERH2-BAMS-ENC_p1   \n", "2        58669      PERH2-BAMS-ENC_p1   \n", "3       615164      PERH2-BAMS-ENC_p1   \n", "4       712599      PERH2-BAMS-ENC_p1   \n", "5       916285      PERH2-BAMS-ENC_p1   \n", "..         ...                    ...   \n", "36  P201501442  PERH_data-BAMS-ENC_p2   \n", "37  P201509880  PERH_data-BAMS-ENC_p2   \n", "38  X201605586  PERH_data-BAMS-ENC_p2   \n", "39  X201610295  PERH_data-BAMS-ENC_p2   \n", "0     ZZZ04911     PERH_data-BAMS-ENC   \n", "\n", "                                                  bam  \n", "0   /.mounts/labs/reimandlab/private/generated_raw...  \n", "2   /.mounts/labs/reimandlab/private/generated_raw...  \n", "3   /.mounts/labs/reimandlab/private/generated_raw...  \n", "4   /.mounts/labs/reimandlab/private/generated_raw...  \n", "5   /.mounts/labs/reimandlab/private/generated_raw...  \n", "..                                                ...  \n", "36  /.mounts/labs/reimandlab/private/generated_raw...  \n", "37  /.mounts/labs/reimandlab/private/generated_raw...  \n", "38  /.mounts/labs/reimandlab/private/generated_raw...  \n", "39  /.mounts/labs/reimandlab/private/generated_raw...  \n", "0   /.mounts/labs/reimandlab/private/generated_raw...  \n", "\n", "[167 rows x 3 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/bin/estonia_2024/sarek'\n", "files = ['sarek_decrypted-data-DNA-ENC.csv']\n", "\n", "\n", "# Initialize an empty list to store the first columns\n", "first_columns = []\n", "\n", "res_df = []\n", "\n", "# Loop through each file\n", "for file in files:\n", "    # Load the DataFrame from the file\n", "    df = pd.read_csv(os.path.join(main_dir, file))\n", "    \n", "    # Assuming the first column name is 'column_name'\n", "    first_column = df.iloc[:, 0].tolist()  # Extract the first column values as a list\n", "    \n", "    # Add the first column values to the list\n", "    first_columns.extend(first_column)\n", "\n", "    # add source details\n", "    if file == 'zsample.csv':\n", "        df['source'] = 'PERH_data-BAMS-ENC'\n", "    else:\n", "        df['source'] = \"_\".join(file.split(\".\")[0].split(\"_\")[1:])\n", "\n", "    res_df.append(df[['patient', 'source']])\n", "\n", "first_columns = np.unique(first_columns)\n", "\n", "res_df = pd.concat(res_df)\n", "mask = ~res_df['patient'].duplicated()\n", "res_df = res_df.loc[mask,:]\n", "\n", "res_df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/_figure_data/somatic_samples.tsv\", sep='\\t', index=False)\n", "res_df\n"]}, {"cell_type": "code", "execution_count": null, "id": "db433494-2cc2-4b33-bc71-94d58a08b2ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "503f4536-1710-440c-9a82-6025de9bdff4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "aca9b2df-6f4d-483f-93b0-f990d0c453bf", "metadata": {}, "outputs": [], "source": ["\n", "\n", "main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/estonia_2024/2024_05_08/somatic_results-nf_decrypted-data-DNA-ENC'\n", "\n", "regex_patterns = ['mutect2.filtered.vcf.gz', 'strelka.variants.vcf.gz', 'freebayes.vcf.gz']\n", "dir_list = ['mutect2_filtered', 'strelka_raw', 'freebayes_raw']\n", "destination_dir_list = [os.path.join(main_dir, 'all_vcfs', x) for x in dir_list]\n", "\n", "\n", "\n", "samples = []\n", "\n", "for i, regex_pattern in enumerate(regex_patterns):\n", "    # make the destination dirs\n", "    if not os.path.exists(destination_dir_list[i]):\n", "        os.makedirs(destination_dir_list[i])\n", "    \n", "    # Function to create symbolic links for files matching the regex pattern\n", "    def create_symbolic_links(source_dir):\n", "        # Search for files matching the regex pattern in the source directory\n", "        files = glob.glob(os.path.join(source_dir, '**', '**', '*'), recursive=True)\n", "        matching_files = [file for file in files if file.endswith(regex_pattern)]\n", "        matching_files.extend([file for file in files if file.endswith(regex_pattern + \".tbi\")])\n", "\n", "        samples.extend(pd.Series(matching_files).str.split(\"/\").str[-2].str.split(\"-\").str[0])\n", "        \n", "        # Create symbolic links for matching files in the destination directory\n", "        for file_path in matching_files:\n", "            file_name = os.path.basename(file_path)\n", "            destination_link = os.path.join(destination_dir_list[i], file_name)\n", "            if not os.path.exists(destination_link):\n", "                os.symlink(file_path, destination_link)\n", "    \n", "    create_symbolic_links(main_dir)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "af996b1a-651c-4437-bd45-c757c5467eff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/plain": ["'X201612680'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["print(len(np.unique(samples)))\n", "'\", \"'.join(np.unique(samples))"]}, {"cell_type": "code", "execution_count": null, "id": "208a6621-ffd4-44f4-a5fa-540f07a32e06", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07a91e36-33c4-4afb-ae99-0e699a91d2b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b08118d4-632c-439e-b906-345d61853252", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d70b7642-b6bc-4294-9b7c-831f5cc4aed8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fefbb492-d0f9-40c8-96a3-8b0ebefd6ee0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f67a274-42d0-4daa-8d2b-16174d7a4b87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "acef4210-1885-4aa9-876e-21ae274feb79", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}