# Alec Bahcheli - <EMAIL>


#####################
# link variants files
#####################

# combine strelka files
rule combine_strelka_vcfs:
    input:
        input_vcf_1 = RES_DIR + "/somatic_all_vcfs/all_vcfs/variant_calling/strelka/{sample}-tumor_vs_{sample}-normal/{sample}-tumor_vs_{sample}-normal.strelka.somatic_indels.vcf.gz",
        input_vcf_2 = RES_DIR + "/somatic_all_vcfs/all_vcfs/variant_calling/strelka/{sample}-tumor_vs_{sample}-normal/{sample}-tumor_vs_{sample}-normal.strelka.somatic_snvs.vcf.gz"

    output:
        combined_vcf = RES_DIR + "/somatic_all_vcfs/all_vcfs/variant_calling/strelka/tumor_vs_normal_combined_strelka/{sample}-tumor_vs_{sample}-normal.strelka.variants.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools concat -o {output.combined_vcf} --allow-overlaps -O z {input.input_vcf_1} {input.input_vcf_2}"""


# link vcf files and create a samples.tsv
rule links_vcfs_and_sample_tsv:
    input:
        expand("{res_dir}/somatic_all_vcfs/all_vcfs/variant_calling/strelka/tumor_vs_normal_combined_strelka/{sample}-tumor_vs_{sample}-normal.strelka.variants.vcf.{suffix}", res_dir = RES_DIR, sample = samples, suffix = ['gz', 'gz.tbi']),

        script = BIN_DIR + "/../somatic_snakemake/001a-link_vcfs.py"

    output:
        expand("{res_dir}/somatic_all_vcfs/strelka_raw/{sample}-tumor.strelka.variants.vcf.{suffix}", res_dir = RES_DIR, sample = samples, suffix = ['gz', 'gz.tbi']),
        expand("{res_dir}/somatic_all_vcfs/freebayes_raw/{sample}-tumor.freebayes.vcf.{suffix}", res_dir = RES_DIR, sample = samples, suffix = ['gz', 'gz.tbi']),
        expand("{res_dir}/somatic_all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.vcf.{suffix}", res_dir = RES_DIR, sample = samples, suffix = ['gz', 'gz.tbi']),

        samples_description_file = FIGURE_DATA_DIR + "/somatic_samples.tsv"

    params:
        results_dir = RES_DIR + "/somatic_all_vcfs"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --samples_description_file {output.samples_description_file} --results_dir {params.results_dir}"


#####################
# variant processing
#####################

# gzip vcf
rule bgzip_vcf:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/{variant_dir}/{variant_file}.vcf"

    output:
        vcf_index = RES_DIR + "/somatic_all_vcfs/{variant_dir}/{variant_file}.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip -k {input.input_vcf}"

# index vcfs
rule index_vcf:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/{variant_dir}/{variant_file}"

    output:
        vcf_index = RES_DIR + "/somatic_all_vcfs/{variant_dir}/{variant_file}.tbi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools index -t {input.input_vcf}"""



#####################
# variant filtering
#####################

# filter freebayes
rule filter_freebayes:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/freebayes_raw/{sample}-tumor.freebayes.vcf.gz"

    output:
        filtered_vcf = RES_DIR + "/somatic_all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -i 'QUAL > 20' {input.input_vcf} -o {output.filtered_vcf}"


# filter strelka 
rule filter_strelka:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/strelka_raw/{sample}-tumor.strelka.variants.vcf.gz"

    output:
        filtered_vcf = RES_DIR + "/somatic_all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -i 'FILTER="PASS"' {input.input_vcf} -o {output.filtered_vcf}"""


# create a unique directory for each sample
rule create_res_dir:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/strelka_raw/{sample}-tumor.strelka.variants.vcf.gz"

    output:
        null_file = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/null.txt"

    params:
        output_dir = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "mkdir -p {params.output_dir} && touch {output.null_file}"


# normalize variant calls
rule normalize_vcfs:
    input:
        genome_fasta = RES_DIR + "/somatic_all_vcfs/Homo_sapiens_assembly38.fasta",

        input_vcf_index = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.vcf.gz.tbi",
        input_vcf = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.vcf.gz"

    output:
        normalized_vcf = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools norm -f {input.genome_fasta} -o {output.normalized_vcf} {input.input_vcf}"


# subset to exomes
rule subset_to_exomes:
    input:
        illumina_exome_bed_file = RES_DIR + '/somatic_all_vcfs/Illumina_Exome_TargetedRegions_v1.2.hg38.bed',

        input_vcf_index = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf.gz.tbi",
        input_vcf = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.vcf.gz"

    output:
        exome_vcf = RES_DIR + "/somatic_all_vcfs/{source}_filtered/{sample}.{source}.filtered.normalized.exome.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -R {input.illumina_exome_bed_file} -o {output.exome_vcf} {input.input_vcf} "



# obtain consensus variants
rule variant_consensus:
    input:
        RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/null.txt",

        freebayes_vcf_index = RES_DIR + "/somatic_all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.normalized.exome.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/somatic_all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.normalized.exome.vcf.gz.tbi",
        mutect2_vcf_index = RES_DIR + "/somatic_all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.normalized.exome.vcf.gz.tbi",


        freebayes_vcf = RES_DIR + "/somatic_all_vcfs/freebayes_filtered/{sample}-tumor.freebayes.filtered.normalized.exome.vcf.gz",
        strelka_vcf = RES_DIR + "/somatic_all_vcfs/strelka_filtered/{sample}-tumor.strelka.filtered.normalized.exome.vcf.gz",
        mutect2_vcf = RES_DIR + "/somatic_all_vcfs/mutect2_filtered/{sample}-tumor.mutect2.filtered.normalized.exome.vcf.gz"

    output:
        RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf",
        RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf",
        RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf"

    params:
        outdir = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools isec -n =2 -p {params.outdir} {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"""



#####################
# variant summaries
#####################

# bcftools summary
rule variant_stats:
    input:
        input_vcf = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}.vcf"

    output:
        summary_file = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}-stats.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools stats {input.input_vcf} > {output.summary_file}"


rule summarize_vcf_variants:
    input:
        expand("{res_dir}/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/{vcf_file}-stats.txt", res_dir = RES_DIR, sample = samples, vcf_file = ['0000', '0001', '0002']),

        sample_recurrence_file = FIGURE_DATA_DIR + "/2023_december_12_NR_and_R_patient_data.csv",
        samples_description_file = FIGURE_DATA_DIR + "/somatic_samples.tsv",

        script = BIN_DIR + "/../somatic_snakemake/001a-vcf_summary.py",
        r_script = BIN_DIR + "/../somatic_snakemake/001a-vcf_summary.R"

    output:
        figure_data_file = RES_DIR + "/_figure_data/001-somatic_vcf_summary.tsv",

        figure_file = RES_DIR + "/_figures/001-somatic_vcf_summary.pdf"

    params:
        results_dir = RES_DIR + "/somatic_all_vcfs"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {params.results_dir} --samples_description_file {input.samples_description_file} --sample_recurrence_file {input.sample_recurrence_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file} --threads {resources.threads}"



rule compare_somatic_to_germline:
    input:
        somatic_data_file = RES_DIR + "/_figure_data/001-somatic_vcf_summary.tsv",
        tumor_data_file = RES_DIR + "/_figure_data/001-vcf_summary.tsv",

        script = BIN_DIR + "/../somatic_snakemake/002a-somatic_summary.py",
        r_script = BIN_DIR + "/../somatic_snakemake/002b-somatic_summary.R"

    output:
        figure_data_file = RES_DIR + "/_figure_data/002-somatic_vs_tumor.tsv",

        figure_file = RES_DIR + "/_figures/002-somatic_vs_tumor.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --somatic_data_file {input.somatic_data_file} --tumor_data_file {input.tumor_data_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"




#####################
# merge VCFs
#####################

# merge vcf
rule merge_vcfs:
    input:
        RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/null.txt",

        mutect2_vcf_index = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz.tbi",
        freebayes_vcf_index = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz.tbi",

        mutect2_vcf = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0000.vcf.gz",
        strelka_vcf = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0001.vcf.gz",
        freebayes_vcf = RES_DIR + "/somatic_all_vcfs/consensus_vcfs/{sample}-tumor/0002.vcf.gz"

    output:
        merged_vcf = RES_DIR + "/somatic_all_vcfs/results/{sample}-tumor_combined.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools concat -o {output.merged_vcf} --allow-overlaps -O v {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"





