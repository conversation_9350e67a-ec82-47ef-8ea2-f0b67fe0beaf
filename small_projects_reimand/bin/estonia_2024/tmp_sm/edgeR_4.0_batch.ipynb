{"cells": [{"cell_type": "code", "execution_count": 2, "id": "c1f6483f-88a4-46eb-a947-18a82ec6de90", "metadata": {"tags": []}, "outputs": [], "source": ["library(edgeR)\n", "library(dplyr)"]}, {"cell_type": "code", "execution_count": 86, "id": "a4ca7a6c-9a8e-47ce-938e-a1d52e7631c0", "metadata": {}, "outputs": [], "source": ["# Read in the count data\n", "x <- read.delim('/.mounts/labs/reimandlab/private/generated_raw_data/TOP2B_RNAseq_RMRP_2024-05-08/results/r_subread/counts.tsv',\n", "                     sep = '\\t', \n", "                     check.names = FALSE)\n", "names(x) <- gsub('.subread.bam', '', names(x))\n", "\n", "# Set the groups by sgRNA\n", "group = c(rep('sgrna_1', 3), rep('sgrna_2', 3), rep('control', 3))\n", "treatment = c(rep('treatment', 6), rep('control', 3))\n"]}, {"cell_type": "code", "execution_count": 87, "id": "b58a1e34-1ceb-469e-9dbd-6fcefabad3ce", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 9</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>6_1835A_S4</th><th scope=col>6_1835B_S5</th><th scope=col>6_1835C_S6</th><th scope=col>6_851A_S8</th><th scope=col>6_851B_S9</th><th scope=col>6_851C_S3</th><th scope=col>Scr_A_S1</th><th scope=col>Scr_B_S2</th><th scope=col>Scr_C_S7</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>DDX11L2</th><td> 9</td><td>11</td><td>17</td><td>20</td><td> 6</td><td>14</td><td>20</td><td>26</td><td>13</td></tr>\n", "\t<tr><th scope=row>DDX11L1</th><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td></tr>\n", "\t<tr><th scope=row>WASH7P</th><td>43</td><td>29</td><td>27</td><td>32</td><td>24</td><td>41</td><td>33</td><td>45</td><td>25</td></tr>\n", "\t<tr><th scope=row>MIR6859-1</th><td> 7</td><td> 3</td><td> 7</td><td> 2</td><td> 0</td><td> 9</td><td> 7</td><td> 7</td><td> 3</td></tr>\n", "\t<tr><th scope=row>MIR1302-2HG</th><td> 0</td><td> 2</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 2</td><td> 0</td><td> 0</td></tr>\n", "\t<tr><th scope=row>MIR1302-2</th><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td><td> 0</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 9\n", "\\begin{tabular}{r|lllllllll}\n", "  & 6\\_1835A\\_S4 & 6\\_1835B\\_S5 & 6\\_1835C\\_S6 & 6\\_851A\\_S8 & 6\\_851B\\_S9 & 6\\_851C\\_S3 & Scr\\_A\\_S1 & Scr\\_B\\_S2 & Scr\\_C\\_S7\\\\\n", "  & <int> & <int> & <int> & <int> & <int> & <int> & <int> & <int> & <int>\\\\\n", "\\hline\n", "\tDDX11L2 &  9 & 11 & 17 & 20 &  6 & 14 & 20 & 26 & 13\\\\\n", "\tDDX11L1 &  0 &  0 &  0 &  0 &  0 &  0 &  0 &  0 &  0\\\\\n", "\tWASH7P & 43 & 29 & 27 & 32 & 24 & 41 & 33 & 45 & 25\\\\\n", "\tMIR6859-1 &  7 &  3 &  7 &  2 &  0 &  9 &  7 &  7 &  3\\\\\n", "\tMIR1302-2HG &  0 &  2 &  0 &  0 &  0 &  0 &  2 &  0 &  0\\\\\n", "\tMIR1302-2 &  0 &  0 &  0 &  0 &  0 &  0 &  0 &  0 &  0\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 9\n", "\n", "| <!--/--> | 6_1835A_S4 &lt;int&gt; | 6_1835B_S5 &lt;int&gt; | 6_1835C_S6 &lt;int&gt; | 6_851A_S8 &lt;int&gt; | 6_851B_S9 &lt;int&gt; | 6_851C_S3 &lt;int&gt; | Scr_A_S1 &lt;int&gt; | Scr_B_S2 &lt;int&gt; | Scr_C_S7 &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|\n", "| DDX11L2 |  9 | 11 | 17 | 20 |  6 | 14 | 20 | 26 | 13 |\n", "| DDX11L1 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |\n", "| WASH7P | 43 | 29 | 27 | 32 | 24 | 41 | 33 | 45 | 25 |\n", "| MIR6859-1 |  7 |  3 |  7 |  2 |  0 |  9 |  7 |  7 |  3 |\n", "| MIR1302-2HG |  0 |  2 |  0 |  0 |  0 |  0 |  2 |  0 |  0 |\n", "| MIR1302-2 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |  0 |\n", "\n"], "text/plain": ["            6_1835A_S4 6_1835B_S5 6_1835C_S6 6_851A_S8 6_851B_S9 6_851C_S3\n", "DDX11L2      9         11         17         20         6        14       \n", "DDX11L1      0          0          0          0         0         0       \n", "WASH7P      43         29         27         32        24        41       \n", "MIR6859-1    7          3          7          2         0         9       \n", "MIR1302-2HG  0          2          0          0         0         0       \n", "MIR1302-2    0          0          0          0         0         0       \n", "            Scr_A_S1 Scr_B_S2 Scr_C_S7\n", "DDX11L2     20       26       13      \n", "DDX11L1      0        0        0      \n", "WASH7P      33       45       25      \n", "MIR6859-1    7        7        3      \n", "MIR1302-2HG  2        0        0      \n", "MIR1302-2    0        0        0      "]}, "metadata": {}, "output_type": "display_data"}], "source": ["counts_df = x[,c(4:6, 7:9, 16:18)]\n", "\n", "head(counts_df)"]}, {"cell_type": "code", "execution_count": 90, "id": "ae2b24f6-a4a5-450c-bc77-38984e0ce25a", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 9 × 4 of type dbl</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>(Intercept)</th><th scope=col>treatmenttreatment</th><th scope=col>groupsgrna_1</th><th scope=col>groupsgrna_2</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>1</td><td>1</td><td>1</td><td>0</td></tr>\n", "\t<tr><th scope=row>2</th><td>1</td><td>1</td><td>1</td><td>0</td></tr>\n", "\t<tr><th scope=row>3</th><td>1</td><td>1</td><td>1</td><td>0</td></tr>\n", "\t<tr><th scope=row>4</th><td>1</td><td>1</td><td>0</td><td>1</td></tr>\n", "\t<tr><th scope=row>5</th><td>1</td><td>1</td><td>0</td><td>1</td></tr>\n", "\t<tr><th scope=row>6</th><td>1</td><td>1</td><td>0</td><td>1</td></tr>\n", "\t<tr><th scope=row>7</th><td>1</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>8</th><td>1</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>9</th><td>1</td><td>0</td><td>0</td><td>0</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 9 × 4 of type dbl\n", "\\begin{tabular}{r|llll}\n", "  & (Intercept) & treatmenttreatment & groupsgrna\\_1 & groupsgrna\\_2\\\\\n", "\\hline\n", "\t1 & 1 & 1 & 1 & 0\\\\\n", "\t2 & 1 & 1 & 1 & 0\\\\\n", "\t3 & 1 & 1 & 1 & 0\\\\\n", "\t4 & 1 & 1 & 0 & 1\\\\\n", "\t5 & 1 & 1 & 0 & 1\\\\\n", "\t6 & 1 & 1 & 0 & 1\\\\\n", "\t7 & 1 & 0 & 0 & 0\\\\\n", "\t8 & 1 & 0 & 0 & 0\\\\\n", "\t9 & 1 & 0 & 0 & 0\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 9 × 4 of type dbl\n", "\n", "| <!--/--> | (Intercept) | treatmenttreatment | groupsgrna_1 | groupsgrna_2 |\n", "|---|---|---|---|---|\n", "| 1 | 1 | 1 | 1 | 0 |\n", "| 2 | 1 | 1 | 1 | 0 |\n", "| 3 | 1 | 1 | 1 | 0 |\n", "| 4 | 1 | 1 | 0 | 1 |\n", "| 5 | 1 | 1 | 0 | 1 |\n", "| 6 | 1 | 1 | 0 | 1 |\n", "| 7 | 1 | 0 | 0 | 0 |\n", "| 8 | 1 | 0 | 0 | 0 |\n", "| 9 | 1 | 0 | 0 | 0 |\n", "\n"], "text/plain": ["  (Intercept) treatmenttreatment groupsgrna_1 groupsgrna_2\n", "1 1           1                  1            0           \n", "2 1           1                  1            0           \n", "3 1           1                  1            0           \n", "4 1           1                  0            1           \n", "5 1           1                  0            1           \n", "6 1           1                  0            1           \n", "7 1           0                  0            0           \n", "8 1           0                  0            0           \n", "9 1           0                  0            0           "]}, "metadata": {}, "output_type": "display_data"}], "source": ["design"]}, {"cell_type": "code", "execution_count": 95, "id": "0be42fd2-bc45-482d-8526-0b9b3b260ba7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using classic mode.\n", "\n"]}, {"ename": "ERROR", "evalue": "Error in glmFit.default(y = y$counts, design = design, dispersion = dispersion, : Design matrix not of full rank.  The following coefficients not estimable:\n groupsgrna_2 treatmenttreatment:groupsgrna_1 treatmenttreatment:groupsgrna_2\n", "output_type": "error", "traceback": ["Error in glmFit.default(y = y$counts, design = design, dispersion = dispersion, : Design matrix not of full rank.  The following coefficients not estimable:\n groupsgrna_2 treatmenttreatment:groupsgrna_1 treatmenttreatment:groupsgrna_2\nTraceback:\n", "1. glm<PERSON>it(y, design)", "2. glmFit.DGEList(y, design)", "3. glmFit(y = y$counts, design = design, dispersion = dispersion, \n .     offset = offset, lib.size = NULL, weights = y$weights, prior.count = prior.count, \n .     start = start, ...)", "4. glmFit.default(y = y$counts, design = design, dispersion = dispersion, \n .     offset = offset, lib.size = NULL, weights = y$weights, prior.count = prior.count, \n .     start = start, ...)", "5. stop(paste(\"Design matrix not of full rank.  The following coefficients not estimable:\\n\", \n .     paste(ne, collapse = \" \")))"]}], "source": ["# minimum number of counts across all samples per gene\n", "min_gene_count = 1 \n", "\n", "# generate data object\n", "y = DGEList(counts_df, group = group)\n", "\n", "# filter for a minimum counts across all samples\n", "keep = filterByExpr(y, min.total.count = min_gene_count * ncol(counts_df))\n", "y = y[keep,,keep.lib.sizes=FALSE]\n", "\n", "# normalize, setup model\n", "y = calcNormFactors(y)\n", "design = model.matrix(~treatment + group)\n", "y = estimateDisp(y,design)\n", "\n", "# # fit quasi-likelihood model\n", "# fit = glmQLFit(y, design)\n", "# lrt = glmTreat(fit, coef=2)\n", "\n", "# # results table of DGEA observations\n", "# res = lrt$table\n", "\n", "# print(head(res))\n", "\n"]}, {"cell_type": "code", "execution_count": 81, "id": "46f2184a-3053-4d51-bac1-4df6d2dc36da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     logFC unshrunk.logFC      logCPM      PValue\n", "ENSG00000269981  0.8582207      0.8645386  0.07254274 0.031908613\n", "VWA1             0.3722988      0.3724410  4.26797247 0.028759443\n", "TMEM52          -0.8288306     -0.8352893 -0.07688049 0.028555121\n", "MORN1           -0.5292885     -0.5305764  1.65732283 0.037655126\n", "PLCH2           -0.4158907     -0.4161465  3.67752884 0.028670484\n", "KCNAB2          -0.6596409     -0.6607908  2.27766267 0.006091466\n"]}], "source": ["print(head(res[res$PValue<0.05,]))"]}, {"cell_type": "code", "execution_count": 84, "id": "dca4648b-478f-468c-b522-65a09403eee3", "metadata": {}, "outputs": [{"data": {"text/html": ["0.518467088934249"], "text/latex": ["0.518467088934249"], "text/markdown": ["0.518467088934249"], "text/plain": ["[1] 0.5184671"]}, "metadata": {}, "output_type": "display_data"}], "source": ["grna = sum(counts_df[rownames(counts_df) == 'ENSG00000269981',1:6]) / 6\n", "control = sum(counts_df[rownames(counts_df) == 'ENSG00000269981',7:9]) / 3\n", "\n", "fc = log2(grna / control)\n", "fc\n"]}, {"cell_type": "code", "execution_count": 85, "id": "be7da41e-8c59-4139-9972-af3240b24b6d", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 1 × 9</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>6_1835A_S4</th><th scope=col>6_1835B_S5</th><th scope=col>6_1835C_S6</th><th scope=col>6_851A_S8</th><th scope=col>6_851B_S9</th><th scope=col>6_851C_S3</th><th scope=col>Scr_A_S1</th><th scope=col>Scr_B_S2</th><th scope=col>Scr_C_S7</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>ENSG00000269981</th><td>23</td><td>25</td><td>28</td><td>8</td><td>14</td><td>8</td><td>15</td><td>14</td><td>8</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 1 × 9\n", "\\begin{tabular}{r|lllllllll}\n", "  & 6\\_1835A\\_S4 & 6\\_1835B\\_S5 & 6\\_1835C\\_S6 & 6\\_851A\\_S8 & 6\\_851B\\_S9 & 6\\_851C\\_S3 & Scr\\_A\\_S1 & Scr\\_B\\_S2 & Scr\\_C\\_S7\\\\\n", "  & <int> & <int> & <int> & <int> & <int> & <int> & <int> & <int> & <int>\\\\\n", "\\hline\n", "\tENSG00000269981 & 23 & 25 & 28 & 8 & 14 & 8 & 15 & 14 & 8\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 1 × 9\n", "\n", "| <!--/--> | 6_1835A_S4 &lt;int&gt; | 6_1835B_S5 &lt;int&gt; | 6_1835C_S6 &lt;int&gt; | 6_851A_S8 &lt;int&gt; | 6_851B_S9 &lt;int&gt; | 6_851C_S3 &lt;int&gt; | Scr_A_S1 &lt;int&gt; | Scr_B_S2 &lt;int&gt; | Scr_C_S7 &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|\n", "| ENSG00000269981 | 23 | 25 | 28 | 8 | 14 | 8 | 15 | 14 | 8 |\n", "\n"], "text/plain": ["                6_1835A_S4 6_1835B_S5 6_1835C_S6 6_851A_S8 6_851B_S9 6_851C_S3\n", "ENSG00000269981 23         25         28         8         14        8        \n", "                Scr_A_S1 Scr_B_S2 Scr_C_S7\n", "ENSG00000269981 15       14       8       "]}, "metadata": {}, "output_type": "display_data"}], "source": ["counts_df[rownames(counts_df) == 'ENSG00000269981',]"]}, {"cell_type": "code", "execution_count": null, "id": "749c0b9b-110d-4167-9a12-a456ba70f855", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.2.2"}}, "nbformat": 4, "nbformat_minor": 5}